#!/usr/bin/env node

/**
 * 运行时错误修复功能测试脚本
 * 
 * 测试运行时错误监控和修复功能是否正常工作
 */

const path = require('path');
const fs = require('fs-extra');
const chalk = require('chalk');
const RuntimeErrorRepairPhase = require('./src/build-time-repair/RuntimeErrorRepairPhase');

async function testRuntimeErrorRepair() {
  console.log(chalk.blue('🧪 开始测试运行时错误修复功能...\n'));

  const testProjectPath = '/Users/<USER>/works/galaxy/vue3-element-plus';
  
  // 创建测试组件文件
  const testComponentPath = path.join(testProjectPath, 'src/components/TestComponent.vue');
  const testComponentContent = `<template>
  <div class="test-component">
    <h2>{{ title }}</h2>
    <p>{{ user.name }}</p>
    <button @click="handleClick">点击测试</button>
  </div>
</template>

<script>
export default {
  name: 'TestComponent',
  data() {
    return {
      title: '测试组件',
      user: null // 这里会导致运行时错误
    }
  },
  methods: {
    // handleClick 方法故意注释掉，会导致运行时错误
    // handleClick() {
    //   console.log('按钮被点击');
    // }
  }
}
</script>

<style scoped>
.test-component {
  padding: 20px;
  border: 1px solid #ccc;
  margin: 10px;
}
</style>`;

  try {
    // 创建测试组件
    await fs.ensureDir(path.dirname(testComponentPath));
    await fs.writeFile(testComponentPath, testComponentContent, 'utf8');
    console.log(chalk.green(`✅ 创建测试组件: ${testComponentPath}`));

    // 启动运行时错误修复
    const runtimePhase = new RuntimeErrorRepairPhase(testProjectPath, {
      timeout: 30000, // 30秒测试
      autoFix: true,
      verbose: true,
      port: 3000,
      devCommand: 'pnpm run dev'
    });

    console.log(chalk.blue('\n🚀 启动运行时错误修复测试...\n'));
    
    const result = await runtimePhase.execute();

    console.log(chalk.blue('\n📊 测试结果:'));
    console.log(chalk.gray(`  成功: ${result.success}`));
    console.log(chalk.gray(`  检测错误: ${result.stats.totalErrors} 个`));
    console.log(chalk.gray(`  修复错误: ${result.stats.fixedErrors} 个`));
    console.log(chalk.gray(`  待处理错误: ${result.stats.pendingErrors} 个`));

    if (result.report) {
      console.log(chalk.blue('\n📋 详细报告:'));
      console.log(chalk.gray(`  监控时长: ${result.report.summary.duration}`));
      console.log(chalk.gray(`  修复成功率: ${result.report.summary.fixRate}`));
    }

    return result;

  } catch (error) {
    console.error(chalk.red(`❌ 测试失败: ${error.message}`));
    return { success: false, error: error.message };
  } finally {
    // 清理测试文件
    try {
      if (await fs.pathExists(testComponentPath)) {
        await fs.remove(testComponentPath);
        console.log(chalk.gray(`🧹 清理测试文件: ${testComponentPath}`));
      }
    } catch (cleanupError) {
      console.warn(chalk.yellow(`⚠️  清理文件失败: ${cleanupError.message}`));
    }
  }
}

// 运行测试
if (require.main === module) {
  testRuntimeErrorRepair()
    .then(result => {
      if (result.success) {
        console.log(chalk.green('\n✅ 运行时错误修复功能测试通过！'));
        process.exit(0);
      } else {
        console.log(chalk.red('\n❌ 运行时错误修复功能测试失败！'));
        process.exit(1);
      }
    })
    .catch(error => {
      console.error(chalk.red(`\n💥 测试异常: ${error.message}`));
      process.exit(1);
    });
}

module.exports = { testRuntimeErrorRepair };
