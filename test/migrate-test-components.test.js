const path = require('path');
const fs = require('fs-extra');
const TestProjectComponentMapper = require('../src/third-party/TestProjectComponentMapper');
const ComponentSearcher = require('../src/third-party/ComponentSearcher');
const MigrationRuleEngine = require('../src/third-party/MigrationRuleEngine');

describe('migrate-test-components', () => {
  const testProjectPath = path.join(__dirname, '..', 'test-project');
  
  describe('TestProjectComponentMapper', () => {
    let mapper;
    
    beforeEach(() => {
      mapper = new TestProjectComponentMapper();
    });

    test('should load test project component mappings', () => {
      const mappings = mapper.getAllMappings();
      
      expect(mappings).toBeDefined();
      expect(Object.keys(mappings).length).toBeGreaterThan(0);
      
      // 检查关键组件
      expect(mappings['vue-count-to']).toBeDefined();
      expect(mappings['vuedraggable']).toBeDefined();
      expect(mappings['vue-splitpane']).toBeDefined();
    });

    test('should provide correct migration targets', () => {
      expect(mapper.getComponentMigration('vue-count-to').target).toBe('vue3-count-to');
      expect(mapper.getComponentMigration('vuedraggable').target).toBe('vuedraggable@4.x');
      expect(mapper.getComponentMigration('vue-splitpane').target).toBe('splitpanes');
    });

    test('should track usage locations in test project', () => {
      const locations = mapper.getUsageLocations('vue-count-to');
      
      expect(locations).toBeDefined();
      expect(Array.isArray(locations)).toBe(true);
      expect(locations.length).toBeGreaterThan(0);
      
      // 应该包含Components.vue的使用
      expect(locations.some(loc => loc.includes('Components.vue'))).toBe(true);
    });

    test('should generate migration summary', () => {
      const summary = mapper.generateMigrationSummary();
      
      expect(summary.totalComponents).toBeGreaterThan(0);
      expect(summary.components).toBeDefined();
      expect(Array.isArray(summary.components)).toBe(true);
      expect(summary.migrationComplexity).toMatch(/simple|medium|complex/);
    });

    test('should assess migration complexity correctly', () => {
      const complexity = mapper.assessMigrationComplexity();
      
      // 由于test-project包含复杂组件，应该是complex
      expect(complexity).toBe('complex');
    });

    test('should identify components used in test project', () => {
      expect(mapper.isUsedInTestProject('vue-count-to')).toBe(true);
      expect(mapper.isUsedInTestProject('vuedraggable')).toBe(true);
      expect(mapper.isUsedInTestProject('non-existent-component')).toBe(false);
    });
  });

  describe('ComponentSearcher with test-project', () => {
    let searcher;
    
    beforeEach(() => {
      searcher = new ComponentSearcher(testProjectPath, {
        useRipgrep: false, // 使用glob确保测试稳定性
        verbose: false
      });
    });

    test('should find components in test project', async () => {
      // 跳过如果test-project不存在
      if (!await fs.pathExists(testProjectPath)) {
        console.log('Skipping test: test-project not found');
        return;
      }

      const result = await searcher.searchAllComponents();
      
      expect(result.components).toBeDefined();
      expect(Array.isArray(result.components)).toBe(true);
      
      // 应该找到一些组件
      if (result.components.length > 0) {
        const componentNames = result.components.map(c => c.name);
        
        // 检查是否找到了预期的组件
        const expectedComponents = ['vue-count-to', 'vuedraggable'];
        const foundExpected = expectedComponents.some(comp => componentNames.includes(comp));
        expect(foundExpected).toBe(true);
      }
    });

    test('should analyze file usage correctly', async () => {
      if (!await fs.pathExists(testProjectPath)) {
        console.log('Skipping test: test-project not found');
        return;
      }

      const componentsVuePath = path.join(testProjectPath, 'src', 'views', 'Components.vue');
      
      if (await fs.pathExists(componentsVuePath)) {
        const fileInfo = await searcher.analyzeFileUsage(componentsVuePath, ['vue-count-to', 'CountTo']);
        
        expect(fileInfo.hasUsage).toBeDefined();
        expect(fileInfo.usages).toBeDefined();
        expect(Array.isArray(fileInfo.usages)).toBe(true);
        expect(fileInfo.complexity).toMatch(/simple|medium|complex/);
      }
    });
  });

  describe('MigrationRuleEngine with test-project components', () => {
    let ruleEngine;
    
    beforeEach(() => {
      ruleEngine = new MigrationRuleEngine({ verbose: false });
    });

    test('should apply rules to vue-count-to', () => {
      const content = `import CountTo from 'vue-count-to'`;
      const component = { name: 'vue-count-to' };
      
      const result = ruleEngine.applyRules(content, component);
      
      expect(result.success).toBe(true);
      expect(result.hasChanges).toBe(true);
      expect(result.content).toContain('vue3-count-to');
      expect(result.appliedRules.length).toBeGreaterThan(0);
    });

    test('should apply rules to vuedraggable', () => {
      const content = `import draggable from 'vuedraggable'`;
      const component = { name: 'vuedraggable' };
      
      const result = ruleEngine.applyRules(content, component);
      
      expect(result.success).toBe(true);
      expect(result.hasChanges).toBe(true);
      expect(result.appliedRules.length).toBeGreaterThan(0);
    });

    test('should apply rules to vue-splitpane', () => {
      const content = `<split-pane><pane>content</pane></split-pane>`;
      const component = { name: 'vue-splitpane' };
      
      const result = ruleEngine.applyRules(content, component);
      
      expect(result.success).toBe(true);
      expect(result.hasChanges).toBe(true);
      expect(result.content).toContain('splitpanes');
    });

    test('should assess complexity for test project components', () => {
      const simpleContent = `import CountTo from 'vue-count-to'`;
      const complexContent = `
        import CountTo from 'vue-count-to'
        export default {
          render(h) {
            return h('div', this.$slots.default)
          }
        }
      `;

      const component = { name: 'vue-count-to' };

      const simpleAssessment = ruleEngine.getComplexityAssessment(simpleContent, component);
      const complexAssessment = ruleEngine.getComplexityAssessment(complexContent, component);

      expect(simpleAssessment.canUseRules).toBe(true);
      expect(complexAssessment.canUseRules).toBe(false);
    });

    test('should handle components not in rules', () => {
      const content = `import SomeComponent from 'unknown-component'`;
      const component = { name: 'unknown-component' };
      
      const result = ruleEngine.applyRules(content, component);
      
      expect(result.success).toBe(true);
      expect(result.hasChanges).toBe(false);
      expect(result.appliedRules.length).toBe(0);
    });
  });

  describe('Integration tests', () => {
    test('should work together - mapper and searcher', async () => {
      if (!await fs.pathExists(testProjectPath)) {
        console.log('Skipping integration test: test-project not found');
        return;
      }

      const mapper = new TestProjectComponentMapper();
      const searcher = new ComponentSearcher(testProjectPath, {
        useRipgrep: false,
        verbose: false
      });

      const mappedComponents = mapper.getUsedComponents();
      const searchResult = await searcher.searchAllComponents();
      
      // 检查映射的组件是否在搜索结果中
      const foundComponents = searchResult.components.map(c => c.name);
      
      // 至少应该有一些重叠
      const overlap = mappedComponents.filter(comp => foundComponents.includes(comp));
      expect(overlap.length).toBeGreaterThan(0);
    });

    test('should work together - mapper and rule engine', () => {
      const mapper = new TestProjectComponentMapper();
      const ruleEngine = new MigrationRuleEngine();
      
      const mappedComponents = mapper.getUsedComponents();
      const supportedComponents = ruleEngine.getSupportedComponents();
      
      // 检查映射的组件中有多少被规则引擎支持
      const supportedMapped = mappedComponents.filter(comp => 
        supportedComponents.includes(comp)
      );
      
      expect(supportedMapped.length).toBeGreaterThan(0);
    });
  });

  describe('Error handling', () => {
    test('should handle non-existent test project gracefully', async () => {
      const nonExistentPath = path.join(__dirname, 'non-existent-project');
      const searcher = new ComponentSearcher(nonExistentPath, {
        useRipgrep: false,
        verbose: false
      });

      // 应该不会抛出异常
      const result = await searcher.searchAllComponents();
      expect(result.components).toBeDefined();
      expect(Array.isArray(result.components)).toBe(true);
    });

    test('should handle invalid component names', () => {
      const mapper = new TestProjectComponentMapper();
      
      expect(mapper.getComponentMigration('invalid-component')).toBeNull();
      expect(mapper.isUsedInTestProject('invalid-component')).toBe(false);
      expect(mapper.getUsageLocations('invalid-component')).toEqual([]);
    });

    test('should handle empty content in rule engine', () => {
      const ruleEngine = new MigrationRuleEngine();
      const component = { name: 'vue-count-to' };
      
      const result = ruleEngine.applyRules('', component);
      
      expect(result.success).toBe(true);
      expect(result.hasChanges).toBe(false);
    });
  });
});
