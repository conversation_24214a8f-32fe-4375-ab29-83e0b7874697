const path = require('path');
const fs = require('fs-extra');
const BuildFixer = require('../src/build/buildFixer');
const ConfigLoader = require('../src/build/configLoader');

describe('BuildFixer Integration Tests', () => {
  let tempDir;
  let buildFixer;

  beforeEach(async () => {
    // 创建临时测试目录
    tempDir = path.join(__dirname, 'temp', `test-${Date.now()}`);
    await fs.ensureDir(tempDir);
    
    // 创建基本的 package.json
    const packageJson = {
      name: 'test-project',
      version: '1.0.0',
      scripts: {
        build: 'echo "Build successful"'
      },
      dependencies: {
        vue: '^3.0.0'
      }
    };
    
    await fs.writeJson(path.join(tempDir, 'package.json'), packageJson, { spaces: 2 });
  });

  afterEach(async () => {
    // 清理临时目录
    if (tempDir && await fs.pathExists(tempDir)) {
      await fs.remove(tempDir);
    }
  });

  describe('Constructor and Initialization', () => {
    test('should create BuildFixer instance with default options', () => {
      buildFixer = new BuildFixer(tempDir);
      
      expect(buildFixer.projectPath).toBe(tempDir);
      expect(buildFixer.configLoader).toBeInstanceOf(ConfigLoader);
      expect(buildFixer.buildStats).toEqual({
        buildAttempts: 0,
        buildSuccess: false,
        errorsFound: [],
        errorsFixed: 0,
        finalBuildSuccess: false,
        startTime: null,
        endTime: null,
        duration: 0
      });
    });

    test('should initialize with user options', async () => {
      const options = {
        buildCommand: 'yarn build',
        maxAttempts: 5,
        verbose: true,
        dryRun: true
      };
      
      buildFixer = new BuildFixer(tempDir, options);
      await buildFixer.initialize();
      
      expect(buildFixer.options.buildCommand).toBe('yarn build');
      expect(buildFixer.options.maxAttempts).toBe(5);
      expect(buildFixer.options.verbose).toBe(true);
      expect(buildFixer.options.dryRun).toBe(true);
    });
  });

  describe('Configuration Loading', () => {
    test('should load configuration from file', async () => {
      const config = {
        buildCommand: 'custom build',
        maxAttempts: 10,
        verbose: true
      };
      
      const configPath = path.join(tempDir, 'build-fixer.config.json');
      await fs.writeJson(configPath, config, { spaces: 2 });
      
      buildFixer = new BuildFixer(tempDir);
      await buildFixer.initialize();
      
      expect(buildFixer.options.buildCommand).toBe('custom build');
      expect(buildFixer.options.maxAttempts).toBe(10);
      expect(buildFixer.options.verbose).toBe(true);
    });

    test('should merge user options with config file', async () => {
      const config = {
        buildCommand: 'config build',
        maxAttempts: 10
      };
      
      const configPath = path.join(tempDir, 'build-fixer.config.json');
      await fs.writeJson(configPath, config, { spaces: 2 });
      
      const userOptions = {
        buildCommand: 'user build', // 应该覆盖配置文件
        verbose: true // 新增选项
      };
      
      buildFixer = new BuildFixer(tempDir, userOptions);
      await buildFixer.initialize();
      
      expect(buildFixer.options.buildCommand).toBe('user build');
      expect(buildFixer.options.maxAttempts).toBe(10);
      expect(buildFixer.options.verbose).toBe(true);
    });
  });

  describe('Progress Indicators', () => {
    test('should handle spinner operations', async () => {
      buildFixer = new BuildFixer(tempDir, { showProgress: true });
      await buildFixer.initialize();

      // 测试启动 spinner
      buildFixer.startSpinner('Testing...');
      expect(buildFixer.spinner).toBeTruthy();

      // 测试更新 spinner
      buildFixer.updateSpinner('Updated...');

      // 测试成功完成
      buildFixer.succeedSpinner('Success!');
      expect(buildFixer.spinner).toBeNull();
    });

    test('should handle spinner with verbose mode', async () => {
      buildFixer = new BuildFixer(tempDir, { verbose: true });
      await buildFixer.initialize();

      // 在 verbose 模式下不应该创建 spinner
      buildFixer.startSpinner('Testing...');
      expect(buildFixer.spinner).toBeNull();
    });
  });

  describe('Error Parsing', () => {
    test('should parse TypeScript errors', () => {
      buildFixer = new BuildFixer(tempDir);

      const errorOutput = `
src/main.ts(10,5): error TS2304: Cannot find name 'Vue'.
src/components/App.vue(15,10): error TS2339: Property 'test' does not exist.
      `;

      const errors = buildFixer.parseErrors(errorOutput);

      expect(errors.length).toBeGreaterThanOrEqual(1);
      expect(errors[0]).toMatchObject({
        type: 'typescript',
        file: 'src/main.ts',
        line: 10,
        column: 5,
        message: expect.stringContaining('Cannot find name')
      });
    });

    test('should parse Vue compilation errors', () => {
      buildFixer = new BuildFixer(tempDir);
      
      const errorOutput = `
src/App.vue:25:15: Template compilation error
src/components/Test.vue:10:5: Invalid component syntax
      `;
      
      const errors = buildFixer.parseErrors(errorOutput);
      
      expect(errors).toHaveLength(2);
      expect(errors[0]).toMatchObject({
        type: 'vue',
        file: 'src/App.vue',
        line: 25,
        column: 15
      });
    });

    test('should categorize errors correctly', () => {
      buildFixer = new BuildFixer(tempDir);

      const errorOutput = `
Error: Cannot find module 'element-ui'
Error: Property 'test' does not exist on type
Error: Vue 2 syntax is not supported
      `;

      const errors = buildFixer.parseErrors(errorOutput);

      // 检查错误分类
      const categories = errors.map(e => e.category);
      expect(categories).toContain('missing-module');
      expect(categories).toContain('property-not-exist');
      expect(categories).toContain('vue-version');
    });
  });

  describe('Module Mapping', () => {
    test('should get module mapping from config', async () => {
      buildFixer = new BuildFixer(tempDir);
      await buildFixer.initialize();
      
      const mapping = buildFixer.getModuleMapping();
      expect(mapping).toBeInstanceOf(Object);
    });
  });

  describe('Build Statistics', () => {
    test('should track build statistics', () => {
      buildFixer = new BuildFixer(tempDir);
      
      const stats = buildFixer.getBuildStats();
      expect(stats).toEqual({
        buildAttempts: 0,
        buildSuccess: false,
        errorsFound: [],
        errorsFixed: 0,
        finalBuildSuccess: false,
        startTime: null,
        endTime: null,
        duration: 0
      });
    });

    test('should create result object correctly', () => {
      buildFixer = new BuildFixer(tempDir);
      buildFixer.buildStats.buildAttempts = 2;
      buildFixer.buildStats.errorsFixed = 3;
      buildFixer.buildStats.duration = 5000;
      
      const result = buildFixer.createResult(true, null, 1);
      
      expect(result).toEqual({
        success: true,
        attempts: 2,
        errorsFixed: 3,
        remainingErrors: 1,
        duration: 5000,
        reason: null
      });
    });
  });

  describe('Dry Run Mode', () => {
    test('should skip operations in dry run mode', async () => {
      buildFixer = new BuildFixer(tempDir, { dryRun: true });
      await buildFixer.initialize();
      
      // 在 dry run 模式下，安装依赖应该被跳过
      await buildFixer.buildExecutor.installDependencies();
      
      // 在 dry run 模式下，构建应该返回成功
      const result = await buildFixer.buildExecutor.executeBuild();
      expect(result.success).toBe(true);
      expect(result.output).toBe('DRY RUN - Build skipped');
    });
  });
});

describe('ConfigLoader Tests', () => {
  let tempDir;
  let configLoader;

  beforeEach(async () => {
    tempDir = path.join(__dirname, 'temp', `config-test-${Date.now()}`);
    await fs.ensureDir(tempDir);
    configLoader = new ConfigLoader();
  });

  afterEach(async () => {
    if (tempDir && await fs.pathExists(tempDir)) {
      await fs.remove(tempDir);
    }
  });

  test('should return default config when no config file exists', async () => {
    const config = await configLoader.loadConfig(null, tempDir);
    
    expect(config).toMatchObject({
      buildCommand: 'npm run build',
      installCommand: 'npm install',
      maxAttempts: 3,
      useLegacyPeerDeps: true
    });
  });

  test('should load and merge config file', async () => {
    const userConfig = {
      buildCommand: 'yarn build',
      maxAttempts: 5,
      customOption: 'test'
    };
    
    const configPath = path.join(tempDir, 'build-fixer.config.json');
    await fs.writeJson(configPath, userConfig, { spaces: 2 });
    
    const config = await configLoader.loadConfig(null, tempDir);
    
    expect(config.buildCommand).toBe('yarn build');
    expect(config.maxAttempts).toBe(5);
    expect(config.customOption).toBe('test');
    expect(config.installCommand).toBe('npm install'); // 默认值保留
  });

  test('should validate config and throw on invalid values', async () => {
    const invalidConfig = {
      buildCommand: '', // 无效：空字符串
      maxAttempts: 15   // 无效：超出范围
    };
    
    expect(() => {
      configLoader.validateConfig(invalidConfig);
    }).toThrow('配置验证失败');
  });

  test('should create default config file', async () => {
    const configPath = await configLoader.createDefaultConfig(tempDir);
    
    expect(await fs.pathExists(configPath)).toBe(true);
    
    const config = await fs.readJson(configPath);
    expect(config).toMatchObject({
      buildCommand: 'npm run build',
      maxAttempts: 3
    });
  });
});
