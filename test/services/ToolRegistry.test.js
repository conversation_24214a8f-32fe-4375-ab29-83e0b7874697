const ToolRegistry = require('../../src/services/tools/ToolRegistry');

describe('ToolRegistry', () => {
  let toolRegistry;

  beforeEach(() => {
    toolRegistry = new ToolRegistry();
  });

  test('should register default tools', () => {
    const tools = toolRegistry.getAllTools();
    expect(tools.length).toBeGreaterThan(0);
    
    const toolNames = tools.map(tool => tool.name);
    expect(toolNames).toContain('read_file');
    expect(toolNames).toContain('write_file');
    expect(toolNames).toContain('list_files');
    expect(toolNames).toContain('run_command');
  });

  test('should get tool description', () => {
    const description = toolRegistry.getToolsDescription();
    expect(description).toContain('read_file');
    expect(description).toContain('write_file');
  });

  test('should validate file path', () => {
    const validPath = toolRegistry.validateFilePath('src/components/Test.vue');
    expect(validPath.valid).toBe(true);

    const invalidPath = toolRegistry.validateFilePath('../../../etc/passwd');
    expect(invalidPath.valid).toBe(false);
  });

  test('should validate command', () => {
    const validCommand = toolRegistry.validateCommand('npm', ['install']);
    expect(validCommand.valid).toBe(true);

    const invalidCommand = toolRegistry.validateCommand('rm', ['-rf', '/']);
    expect(invalidCommand.valid).toBe(false);
  });

  test('should register custom tool', () => {
    toolRegistry.registerTool({
      name: 'custom_tool',
      category: 'test',
      description: 'A custom test tool',
      parameters: {
        type: 'object',
        properties: {
          param1: { type: 'string' }
        }
      }
    });

    expect(toolRegistry.hasTool('custom_tool')).toBe(true);
    const tool = toolRegistry.getTool('custom_tool');
    expect(tool.name).toBe('custom_tool');
    expect(tool.category).toBe('test');
  });

  test('should get tools by category', () => {
    const fileTools = toolRegistry.getToolsByCategory('file');
    expect(fileTools.length).toBeGreaterThan(0);
    
    const commandTools = toolRegistry.getToolsByCategory('command');
    expect(commandTools.length).toBeGreaterThan(0);
  });

  test('should get stats', () => {
    const stats = toolRegistry.getStats();
    expect(stats.totalTools).toBeGreaterThan(0);
    expect(stats.categories).toContain('file');
    expect(stats.categories).toContain('command');
  });
});
