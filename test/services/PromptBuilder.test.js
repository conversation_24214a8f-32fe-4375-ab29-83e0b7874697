const PromptBuilder = require('../../src/services/ai/PromptBuilder');
const ToolRegistry = require('../../src/services/tools/ToolRegistry');

describe('PromptBuilder', () => {
  let promptBuilder;
  let toolRegistry;

  beforeEach(() => {
    toolRegistry = new ToolRegistry();
    promptBuilder = new PromptBuilder(toolRegistry);
  });

  test('should build error analysis prompt', () => {
    const buildOutput = 'Error: Cannot find module "vue"';
    const context = { attemptNumber: 1 };
    
    const prompt = promptBuilder.buildErrorAnalysisPrompt(buildOutput, context);
    
    expect(prompt).toContain('Vue 2 到 Vue 3 迁移专家');
    expect(prompt).toContain(buildOutput);
    expect(prompt).toContain('read_file');
  });

  test('should build tool call prompt', () => {
    const filePath = 'src/components/Test.vue';
    const buildOutput = 'Error: Cannot find module "vue"';
    const context = { attemptNumber: 1 };
    
    const prompt = promptBuilder.buildToolCallPrompt(filePath, buildOutput, context);
    
    expect(prompt).toContain(filePath);
    expect(prompt).toContain(buildOutput);
    expect(prompt).toContain('tool_calls');
  });

  test('should build file fix prompt', () => {
    const filePath = 'src/components/Test.vue';
    const fileContent = '<template><div>Test</div></template>';
    const buildOutput = 'Error: Cannot find module "vue"';
    const contextFiles = {};
    const context = { attemptNumber: 1, fileIndex: 1, totalFiles: 1 };
    
    const prompt = promptBuilder.buildFileFixPrompt(
      filePath, fileContent, buildOutput, contextFiles, context
    );
    
    expect(prompt).toContain(filePath);
    expect(prompt).toContain(fileContent);
    expect(prompt).toContain(buildOutput);
    expect(prompt).toContain('fix_result');
  });

  test('should build webpack fix prompt for webpack files', () => {
    const filePath = 'vue.config.js';
    const fileContent = 'module.exports = {}';
    const buildOutput = 'Error: webpack is not defined';
    const contextFiles = {};
    const context = { attemptNumber: 1 };
    
    const prompt = promptBuilder.buildFileFixPrompt(
      filePath, fileContent, buildOutput, contextFiles, context
    );
    
    expect(prompt).toContain('Webpack');
    expect(prompt).toContain('配置专家');
  });

  test('should build vue fix prompt for vue files', () => {
    const filePath = 'src/components/Test.vue';
    const fileContent = '<template><div>Test</div></template>';
    const buildOutput = 'Error: Vue 2 syntax not supported';
    const contextFiles = {};
    const context = { attemptNumber: 1 };
    
    const prompt = promptBuilder.buildFileFixPrompt(
      filePath, fileContent, buildOutput, contextFiles, context
    );
    
    expect(prompt).toContain('Vue 2 到 Vue 3');
    expect(prompt).toContain('Composition API');
  });

  test('should truncate long output', () => {
    const longOutput = 'A'.repeat(10000);
    const context = { attemptNumber: 1 };
    
    const prompt = promptBuilder.buildErrorAnalysisPrompt(longOutput, context);
    
    expect(prompt).toContain('输出已截断');
    expect(prompt.length).toBeLessThan(longOutput.length + 1000);
  });

  test('should format context files', () => {
    const contextFiles = {
      'src/utils/helper.js': 'export function helper() { return "test"; }'
    };
    
    const formatted = promptBuilder.formatContextFiles(contextFiles);
    
    expect(formatted).toContain('src/utils/helper.js');
    expect(formatted).toContain('export function helper');
  });

  test('should format previous attempts', () => {
    const attempts = [
      { error: 'Syntax error' },
      { error: 'Import error' }
    ];
    
    const formatted = promptBuilder.formatPreviousAttempts(attempts);
    
    expect(formatted).toContain('尝试 1');
    expect(formatted).toContain('Syntax error');
    expect(formatted).toContain('尝试 2');
    expect(formatted).toContain('Import error');
  });
});
