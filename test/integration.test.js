const BuildFixer = require('../src/build/buildFixer');
const AIRepairer = require('../src/ai/aiRepairer');
const { AIService } = require('../src/ai/ai-service');

// Mock environment variables for testing
process.env.DEEPSEEK_TOKEN = 'test-token';

describe('Integration Tests', () => {
  let buildFixer;
  let aiRepairer;

  beforeEach(() => {
    aiRepairer = new AIRepairer();
    buildFixer = new BuildFixer('/test/path', {
      buildCommand: 'echo "test"',
      aiRepairer: aiRepairer
    });
  });

  it('should integrate BuildFixer with AIRepairer', () => {
    expect(buildFixer.options.aiRepairer).toBe(aiRepairer);
  });

  it('should have BuildExecutor instance', async () => {
    await buildFixer.initialize();
    expect(buildFixer.buildExecutor).toBeDefined();
    expect(typeof buildFixer.buildExecutor.performBuild).toBe('function');
  });
});
