#!/usr/bin/env node

/**
 * 运行时错误修复功能完整测试脚本
 * 包含错误模拟和AI修复验证
 */

const path = require('path');
const chalk = require('chalk');
const RuntimeErrorRepairPhase = require('./src/build-time-repair/RuntimeErrorRepairPhase');

async function testRuntimeErrorRepairWithAI() {
  console.log(chalk.blue('🧪 测试运行时错误修复功能（包含AI修复）...\n'));

  const testProjectPath = '/Users/<USER>/works/galaxy/vue3-element-plus';
  
  try {
    // 创建运行时错误修复阶段
    const runtimePhase = new RuntimeErrorRepairPhase(testProjectPath, {
      timeout: 30000, // 30秒测试
      autoFix: true,  // 启用自动修复
      verbose: true,
      port: 3002,     // 使用不同端口避免冲突
      simulateErrors: true // 启用错误模拟
    });

    console.log(chalk.gray('配置信息:'));
    console.log(chalk.gray(`  项目路径: ${testProjectPath}`));
    console.log(chalk.gray(`  监控时间: 30秒`));
    console.log(chalk.gray(`  自动修复: 启用`));
    console.log(chalk.gray(`  端口: 3002`));
    console.log(chalk.gray(`  错误模拟: 启用`));
    console.log(chalk.gray(`  详细输出: 启用\n`));

    // 执行运行时错误修复
    console.log(chalk.blue('🚀 开始执行运行时错误修复测试...'));
    const result = await runtimePhase.execute();

    // 显示详细结果
    console.log(chalk.blue('\n📊 测试结果详情:'));
    console.log(chalk.gray(`  执行成功: ${result.success ? '是' : '否'}`));
    
    if (result.stats) {
      console.log(chalk.gray(`  检测错误: ${result.stats.totalErrors} 个`));
      console.log(chalk.gray(`  修复错误: ${result.stats.fixedErrors} 个`));
      console.log(chalk.gray(`  待处理错误: ${result.stats.pendingErrors} 个`));
      
      if (result.stats.totalErrors > 0) {
        const fixRate = (result.stats.fixedErrors / result.stats.totalErrors * 100).toFixed(1);
        console.log(chalk.gray(`  修复成功率: ${fixRate}%`));
      }
    }

    if (result.report) {
      console.log(chalk.gray(`  监控时长: ${result.report.summary.duration}`));
      console.log(chalk.gray(`  自动修复: ${result.report.details.autoFixEnabled ? '启用' : '禁用'}`));
    }

    if (result.error) {
      console.log(chalk.red(`  错误信息: ${result.error}`));
    }

    // 分析测试结果
    console.log(chalk.blue('\n🔍 结果分析:'));
    
    if (result.success) {
      if (result.stats && result.stats.totalErrors > 0) {
        console.log(chalk.green('✅ 运行时错误监控功能正常工作'));
        console.log(chalk.green(`✅ 成功检测到 ${result.stats.totalErrors} 个运行时错误`));
        
        if (result.stats.fixedErrors > 0) {
          console.log(chalk.green(`✅ AI修复功能正常工作，修复了 ${result.stats.fixedErrors} 个错误`));
        } else {
          console.log(chalk.yellow('⚠️  AI修复功能未触发或修复失败'));
        }
        
        if (result.stats.pendingErrors > 0) {
          console.log(chalk.yellow(`⚠️  还有 ${result.stats.pendingErrors} 个错误待处理`));
        }
      } else {
        console.log(chalk.yellow('⚠️  未检测到运行时错误（可能是错误模拟未生效）'));
      }
    } else {
      console.log(chalk.red('❌ 运行时错误修复功能测试失败'));
    }

    // 功能验证总结
    console.log(chalk.blue('\n📋 功能验证总结:'));
    
    const checks = [
      {
        name: '开发服务器启动',
        passed: !result.error || !result.error.includes('开发服务器启动'),
        critical: true
      },
      {
        name: '错误监控端点',
        passed: result.success,
        critical: true
      },
      {
        name: '错误检测功能',
        passed: result.stats && result.stats.totalErrors > 0,
        critical: false
      },
      {
        name: 'AI修复功能',
        passed: result.stats && result.stats.fixedErrors > 0,
        critical: false
      },
      {
        name: '报告生成功能',
        passed: result.report && result.report.summary,
        critical: true
      }
    ];

    checks.forEach(check => {
      const icon = check.passed ? '✅' : (check.critical ? '❌' : '⚠️');
      const color = check.passed ? chalk.green : (check.critical ? chalk.red : chalk.yellow);
      console.log(color(`  ${icon} ${check.name}: ${check.passed ? '通过' : '失败'}`));
    });

    // 最终结论
    const criticalPassed = checks.filter(c => c.critical).every(c => c.passed);
    const allPassed = checks.every(c => c.passed);

    console.log(chalk.blue('\n🎯 最终结论:'));
    
    if (allPassed) {
      console.log(chalk.green('🎉 所有功能测试通过！运行时错误修复功能完全正常！'));
    } else if (criticalPassed) {
      console.log(chalk.yellow('⚠️  核心功能正常，部分功能需要优化'));
      console.log(chalk.gray('   建议：检查错误模拟和AI修复配置'));
    } else {
      console.log(chalk.red('❌ 核心功能存在问题，需要修复'));
    }

    // 使用建议
    if (criticalPassed) {
      console.log(chalk.blue('\n💡 使用建议:'));
      console.log(chalk.gray('  1. 在实际项目中使用时，可以禁用错误模拟'));
      console.log(chalk.gray('  2. 根据项目大小调整监控时间'));
      console.log(chalk.gray('  3. 在生产环境中谨慎使用自动修复'));
      console.log(chalk.gray('  4. 定期检查修复日志和统计报告'));
    }

  } catch (error) {
    console.error(chalk.red('\n❌ 测试执行失败:'), error.message);
    
    if (error.message.includes('开发服务器启动')) {
      console.log(chalk.yellow('\n💡 可能的解决方案:'));
      console.log(chalk.gray('  • 确保项目依赖已正确安装'));
      console.log(chalk.gray('  • 检查端口是否被占用'));
      console.log(chalk.gray('  • 增加服务器启动超时时间'));
      console.log(chalk.gray('  • 检查项目的dev命令是否正确'));
    }
    
    if (error.stack && process.env.VERBOSE) {
      console.log(chalk.gray('\n详细错误堆栈:'));
      console.log(chalk.gray(error.stack));
    }
    
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testRuntimeErrorRepairWithAI().catch(console.error);
}

module.exports = { testRuntimeErrorRepairWithAI };
