/**
 * Webpack配置示例：集成运行时错误修复功能
 * 
 * 这个配置文件展示了如何按照README.md的建议，
 * 将运行时错误监控和修复功能集成到webpack-dev-server中
 */

const path = require('path');
const express = require('express');
const RuntimeErrorHandler = require('../src/build-time-repair/RuntimeErrorHandler');

// 初始化运行时错误处理器
const runtimeErrorHandler = new RuntimeErrorHandler(process.cwd(), {
  port: 3000,
  errorEndpoint: '/__runtime_errors__',
  maxErrors: 100,
  autoFix: true,
  verbose: process.env.NODE_ENV === 'development'
});

module.exports = {
  // 其他webpack配置...
  mode: 'development',
  
  entry: {
    main: './src/main.js',
  },

  // 按照README.md建议：配置高质量的Source Maps
  devtool: 'source-map', // 或 'inline-source-map'

  devServer: {
    port: 3000,
    hot: true,
    
    // 按照README.md建议：使用setupMiddlewares扩展webpack-dev-server
    setupMiddlewares: (middlewares, devServer) => {
      if (!devServer) {
        throw new Error('webpack-dev-server is not defined');
      }

      console.log('🔧 正在集成运行时错误修复功能...');

      // 1. 使用express.json()中间件来解析POST请求的JSON体
      devServer.app.use(express.json());

      // 2. 添加运行时错误处理路由
      devServer.app.use(runtimeErrorHandler.getRouter());

      // 3. 定义AI修复端点（按照README.md的建议）
      devServer.app.post('/api/v1/fix-code', async (req, res) => {
        try {
          console.log('🚨 收到运行时错误修复请求');
          
          // 这里应该调用完整的错误分析和AI修复逻辑
          // const result = await handleCodeFixRequest(req.body);
          
          // 临时响应，实际实现需要集成完整的修复流程
          res.status(200).json({ 
            success: true, 
            message: 'Error received and queued for AI analysis' 
          });

          // 按照README.md建议：成功修复后触发HMR
          // const devServerUrl = `http://${devServer.options.host}:${devServer.options.port}`;
          // await fetch(`${devServerUrl}/webpack-dev-server/invalidate`);
          
        } catch (error) {
          console.error('❌ 运行时错误修复失败:', error);
          res.status(error.statusCode || 500).json({ 
            success: false, 
            message: error.message 
          });
        }
      });

      console.log('✅ 运行时错误修复功能已集成到webpack-dev-server');
      console.log(`🔍 错误监控端点: http://localhost:3000/__runtime_errors__`);
      console.log(`🤖 AI修复端点: http://localhost:3000/api/v1/fix-code`);

      // 必须返回中间件数组
      return middlewares;
    },
  },

  // 按照README.md建议：使用webpack插件注入错误处理代码
  plugins: [
    // 注意：需要安装 webpack-inject-entry-plugin
    // npm install webpack-inject-entry-plugin --save-dev
    
    // new (require('webpack-inject-entry-plugin'))({
    //   entry: "main",
    //   filepath: path.resolve(__dirname, './src/error-dispatcher.js'),
    // }),

    // 其他插件...
  ],

  // 其他配置...
  resolve: {
    extensions: ['.js', '.vue', '.ts'],
  },

  module: {
    rules: [
      {
        test: /\.vue$/,
        loader: 'vue-loader'
      },
      {
        test: /\.js$/,
        loader: 'babel-loader',
        exclude: /node_modules/
      },
      // 其他规则...
    ]
  }
};

/**
 * 使用说明：
 * 
 * 1. 将此配置复制到你的项目根目录，重命名为 webpack.config.js
 * 2. 安装必要的依赖：
 *    npm install webpack-inject-entry-plugin --save-dev
 * 3. 创建 src/error-dispatcher.js 文件（参考README.md第1.2节）
 * 4. 启动开发服务器：npm run dev 或 webpack serve
 * 5. 运行时错误将自动被捕获并发送到AI修复端点
 * 
 * 注意事项：
 * - 确保已配置OpenAI API密钥（OPENAI_API_KEY环境变量）
 * - Source Maps质量直接影响错误定位精度
 * - 生产环境中应禁用此功能
 */
