const path = require('path');
const ToolExecutor = require('../src/ai/ToolExecutor');

/**
 * run_command 工具使用示例
 * 演示如何在 ToolExecutor 中执行命令行操作
 */
async function runCommandExample() {
  console.log('🚀 ToolExecutor run_command 工具示例\n');

  // 创建工具执行器实例
  const projectPath = path.join(__dirname, '..');
  const toolExecutor = new ToolExecutor(projectPath, {
    verbose: true,
    dryRun: false,
    commandTimeout: 10000
  });

  console.log('📋 当前允许的命令:', toolExecutor.getAllowedCommands().join(', '));
  console.log();

  // 示例 1: 执行 npm --version
  console.log('=== 示例 1: 检查 npm 版本 ===');
  try {
    const result1 = await toolExecutor.executeToolCall('run_command', {
      command: 'npm --version'
    });
    
    if (result1.success) {
      console.log('✅ npm 版本:', result1.stdout);
    } else {
      console.log('❌ 执行失败:', result1.error);
    }
  } catch (error) {
    console.log('❌ 异常:', error.message);
  }
  console.log();

  // 示例 2: 执行 node --version
  console.log('=== 示例 2: 检查 Node.js 版本 ===');
  try {
    const result2 = await toolExecutor.executeToolCall('run_command', {
      command: 'node',
      args: ['--version']
    });
    
    if (result2.success) {
      console.log('✅ Node.js 版本:', result2.stdout);
    } else {
      console.log('❌ 执行失败:', result2.error);
    }
  } catch (error) {
    console.log('❌ 异常:', error.message);
  }
  console.log();

  // 示例 3: 列出当前目录文件 (使用相对工作目录)
  console.log('=== 示例 3: 列出 src 目录内容 ===');
  try {
    const result3 = await toolExecutor.executeToolCall('run_command', {
      command: 'ls -la',
      working_directory: 'src'
    });
    
    if (result3.success) {
      console.log('✅ src 目录内容:');
      console.log(result3.stdout);
    } else {
      console.log('❌ 执行失败:', result3.error);
    }
  } catch (error) {
    console.log('❌ 异常:', error.message);
  }
  console.log();

  // 示例 4: 尝试执行不被允许的命令
  console.log('=== 示例 4: 尝试执行不允许的命令 ===');
  try {
    const result4 = await toolExecutor.executeToolCall('run_command', {
      command: 'python --version'
    });
    
    if (result4.success) {
      console.log('✅ Python 版本:', result4.stdout);
    } else {
      console.log('❌ 执行失败 (预期):', result4.error);
    }
  } catch (error) {
    console.log('❌ 异常:', error.message);
  }
  console.log();

  // 示例 5: 动态添加允许的命令
  console.log('=== 示例 5: 动态添加允许的命令 ===');
  console.log('添加 python 到允许列表...');
  toolExecutor.addAllowedCommand('python');
  
  try {
    const result5 = await toolExecutor.executeToolCall('run_command', {
      command: 'python --version'
    });
    
    if (result5.success) {
      console.log('✅ Python 版本:', result5.stdout);
    } else {
      console.log('❌ 执行失败:', result5.error);
    }
  } catch (error) {
    console.log('❌ 异常:', error.message);
  }
  console.log();

  // 示例 6: Dry Run 模式
  console.log('=== 示例 6: Dry Run 模式 ===');
  const dryRunExecutor = new ToolExecutor(projectPath, {
    verbose: true,
    dryRun: true
  });

  try {
    const result6 = await dryRunExecutor.executeToolCall('run_command', {
      command: 'npm install'
    });
    
    console.log('Dry Run 结果:', {
      success: result6.success,
      dryRun: result6.dryRun,
      command: result6.command,
      args: result6.args,
      message: result6.message
    });
  } catch (error) {
    console.log('❌ 异常:', error.message);
  }
  console.log();

  // 示例 7: 批量执行工具调用
  console.log('=== 示例 7: 批量执行多个命令 ===');
  const toolCalls = [
    {
      name: 'run_command',
      parameters: { command: 'echo "Hello from command 1"' }
    },
    {
      name: 'run_command', 
      parameters: { command: 'echo "Hello from command 2"' }
    },
    {
      name: 'run_command',
      parameters: { command: 'pwd' }
    }
  ];

  try {
    const batchResults = await toolExecutor.executeToolCalls(toolCalls);
    
    console.log('📊 批量执行统计:');
    const stats = toolExecutor.getExecutionStats(batchResults);
    console.log(`总计: ${stats.total}, 成功: ${stats.success}, 失败: ${stats.failed}`);
    
    // 格式化上下文信息
    const contextInfo = toolExecutor.formatContextFiles(batchResults);
    if (contextInfo) {
      console.log('\n📄 执行结果上下文:');
      console.log(contextInfo);
    }
  } catch (error) {
    console.log('❌ 异常:', error.message);
  }

  console.log('\n✨ 示例执行完成！');
}

// 如果直接运行此文件，则执行示例
if (require.main === module) {
  runCommandExample().catch(console.error);
}

module.exports = runCommandExample; 