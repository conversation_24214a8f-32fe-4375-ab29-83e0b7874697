/**
 * Vue运行时错误调度器
 * 
 * 按照README.md第1.2节的建议实现的客户端错误处理脚本
 * 这个脚本应该通过webpack插件自动注入到应用的主入口点
 */

// 检查Vue是否可用
if (typeof window !== 'undefined' && window.Vue) {
  console.log('🔧 正在初始化运行时错误监控...');
  
  // 获取Vue应用实例（Vue 3）
  const app = window.Vue.createApp ? window.Vue.createApp({}) : null;
  
  if (app) {
    // Vue 3 错误处理器配置
    app.config.errorHandler = (err, instance, info) => {
      // 1. 原始错误日志，保留开发时的控制台输出
      console.error("🚨 Caught by AI Fixer:", err);
      
      // 2. 构建发送到服务器的负载
      const payload = {
        message: err.message,
        stack: err.stack,
        info: info,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        
        // 序列化组件上下文，注意避免循环引用
        context: {
          componentName: instance ? (instance.$options?.name || instance.type?.name || 'Unknown') : 'Unknown',
          props: instance ? safeSerialize(instance.$props) : null,
          data: instance ? safeSerialize(instance.$data) : null,
          // Vue 3 特有的响应式数据
          setupState: instance ? safeSerialize(instance.setupState) : null
        }
      };
      
      // 3. 使用fetch API将错误数据POST到开发服务器的自定义端点
      sendErrorToServer(payload);
    };
    
    // Vue 3 警告处理器
    app.config.warnHandler = (msg, instance, trace) => {
      const payload = {
        message: msg,
        stack: trace,
        info: 'vue-warning',
        timestamp: new Date().toISOString(),
        type: 'warning',
        context: {
          componentName: instance ? (instance.$options?.name || instance.type?.name || 'Unknown') : 'Unknown',
          trace: trace
        }
      };
      
      console.warn("⚠️ Vue Warning caught by AI Fixer:", msg);
      sendErrorToServer(payload);
    };
  }
}

// 全局JavaScript错误处理器
window.addEventListener('error', function(event) {
  const payload = {
    message: event.message,
    stack: event.error ? event.error.stack : '',
    info: 'global-error',
    timestamp: new Date().toISOString(),
    fileName: event.filename,
    lineNumber: event.lineno,
    columnNumber: event.colno,
    type: 'javascript-error',
    context: {
      source: 'window.onerror'
    }
  };
  
  console.error("🚨 Global Error caught by AI Fixer:", event.error || event.message);
  sendErrorToServer(payload);
});

// Promise rejection处理器
window.addEventListener('unhandledrejection', function(event) {
  const payload = {
    message: event.reason ? (event.reason.message || event.reason.toString()) : 'Unhandled Promise Rejection',
    stack: event.reason ? event.reason.stack : '',
    info: 'unhandled-promise-rejection',
    timestamp: new Date().toISOString(),
    type: 'promise-rejection',
    context: {
      reason: safeSerialize(event.reason),
      source: 'unhandledrejection'
    }
  };
  
  console.error("🚨 Unhandled Promise Rejection caught by AI Fixer:", event.reason);
  sendErrorToServer(payload);
});

/**
 * 安全序列化对象，避免循环引用
 */
function safeSerialize(obj, maxDepth = 3, currentDepth = 0) {
  if (currentDepth >= maxDepth) {
    return '[Max Depth Reached]';
  }
  
  if (obj === null || obj === undefined) {
    return obj;
  }
  
  if (typeof obj === 'function') {
    return '[Function]';
  }
  
  if (typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return obj.toISOString();
  }
  
  if (Array.isArray(obj)) {
    return obj.slice(0, 10).map(item => safeSerialize(item, maxDepth, currentDepth + 1));
  }
  
  const result = {};
  const keys = Object.keys(obj).slice(0, 20); // 限制属性数量
  
  for (const key of keys) {
    try {
      result[key] = safeSerialize(obj[key], maxDepth, currentDepth + 1);
    } catch (error) {
      result[key] = '[Serialization Error]';
    }
  }
  
  return result;
}

/**
 * 发送错误到服务器
 */
function sendErrorToServer(payload) {
  // 按照README.md建议：发送到webpack-dev-server的自定义端点
  fetch('/api/v1/fix-code', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payload),
  })
  .then(response => {
    if (response.ok) {
      console.log('✅ 错误报告已发送到AI修复服务');
    } else {
      console.warn('⚠️ 错误报告发送失败:', response.status);
    }
  })
  .catch(networkError => {
    console.error('❌ AI Fixer: Failed to send error report to server.', networkError);
  });
}

console.log('✅ 运行时错误监控已初始化');

/**
 * 导出配置对象（如果在模块环境中使用）
 */
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    sendErrorToServer,
    safeSerialize
  };
}
