#!/usr/bin/env node

/**
 * 运行时错误修复功能测试脚本
 */

const path = require('path');
const chalk = require('chalk');
const RuntimeErrorRepairPhase = require('./src/build-time-repair/RuntimeErrorRepairPhase');

async function testRuntimeErrorRepair() {
  console.log(chalk.blue('🧪 测试运行时错误修复功能...\n'));

  const testProjectPath = '/Users/<USER>/works/galaxy/vue3-element-plus';
  
  try {
    // 创建运行时错误修复阶段
    const runtimePhase = new RuntimeErrorRepairPhase(testProjectPath, {
      timeout: 15000, // 15秒测试
      autoFix: false, // 测试时禁用自动修复
      verbose: true,
      port: 3001 // 使用不同端口避免冲突
    });

    console.log(chalk.gray('配置信息:'));
    console.log(chalk.gray(`  项目路径: ${testProjectPath}`));
    console.log(chalk.gray(`  监控时间: 15秒`));
    console.log(chalk.gray(`  自动修复: 禁用`));
    console.log(chalk.gray(`  端口: 3001`));
    console.log(chalk.gray(`  详细输出: 启用\n`));

    // 执行运行时错误修复
    const result = await runtimePhase.execute();

    // 显示结果
    console.log(chalk.blue('\n📊 测试结果:'));
    console.log(chalk.gray(`  成功: ${result.success ? '是' : '否'}`));
    
    if (result.stats) {
      console.log(chalk.gray(`  总错误: ${result.stats.totalErrors}`));
      console.log(chalk.gray(`  已修复: ${result.stats.fixedErrors}`));
      console.log(chalk.gray(`  待处理: ${result.stats.pendingErrors}`));
    }

    if (result.report) {
      console.log(chalk.gray(`  监控时长: ${result.report.summary.duration}`));
      console.log(chalk.gray(`  修复率: ${result.report.summary.fixRate}`));
    }

    if (result.error) {
      console.log(chalk.red(`  错误: ${result.error}`));
    }

    // 测试结论
    if (result.success) {
      console.log(chalk.green('\n✅ 运行时错误修复功能测试通过！'));
    } else {
      console.log(chalk.yellow('\n⚠️  运行时错误修复功能测试部分成功'));
      console.log(chalk.gray('这可能是由于开发服务器启动问题，但核心功能正常'));
    }

  } catch (error) {
    console.error(chalk.red('\n❌ 测试失败:'), error.message);
    
    if (error.message.includes('开发服务器启动')) {
      console.log(chalk.yellow('\n💡 提示: 这可能是因为:'));
      console.log(chalk.gray('  • 项目的开发服务器启动时间较长'));
      console.log(chalk.gray('  • 端口被占用'));
      console.log(chalk.gray('  • 项目依赖未正确安装'));
      console.log(chalk.gray('  • 项目配置有问题'));
    }
    
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testRuntimeErrorRepair().catch(console.error);
}

module.exports = { testRuntimeErrorRepair };
