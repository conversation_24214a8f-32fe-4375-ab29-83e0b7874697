const { execSync } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const ora = require('ora');
const BuildFixAgent = require('../ai/BuildFixAgent');
const ConfigLoader = require('./configLoader');
const WebpackCodemodMigrator = require('../webpack/WebpackCodemodMigrator');
const WebpackConfigDetector = require('../webpack/utils/webpackConfigDetector');
const BuildExecutor = require('./BuildExecutor');
const RuntimeErrorRepairPhase = require('../build-time-repair/RuntimeErrorRepairPhase');

/**
 * AI Agent 构建错误修复器
 * 基于强大的 AI Agent 能力，自动分析和修复构建错误
 *
 * 核心特性：
 * - AI 驱动的错误分析，无需预定义分类
 * - 两阶段修复：文件选择 → 文件修改
 * - 工具调用机制，类似 Augment Agent
 * - 简化的配置和更强的适应性
 */

class BuildFixer {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.userOptions = options;

    // 初始化基本选项，避免测试失败
    this.options = {
      buildCommand: 'npm run build',
      devCommand: 'npm run dev',
      installCommand: 'npm install',
      maxAttempts: 6,
      mode: 'build',
      devTimeout: 30000,
      legacyPeerDeps: true,
      skipInstall: false,
      skipAI: false,
      dryRun: false,
      interactive: false,
      explain: false,
      verbose: false,
      maxRetries: 3,
      // 运行时错误修复选项
      enableRuntimeRepair: false, // 默认禁用，需要明确启用
      runtimeTimeout: 60000, // 运行时监控超时时间（1分钟）
      runtimeAutoFix: true, // 是否自动修复运行时错误
      runtimePort: 3000, // 开发服务器端口
      ...options
    };

    // 配置加载器
    this.configLoader = new ConfigLoader();

    // 构建执行器
    this.buildExecutor = null;

    // 构建统计
    this.buildStats = {
      startTime: null,
      endTime: null,
      duration: 0,
      buildAttempts: 0,
      buildSuccess: false,
      finalBuildSuccess: false,
      errorsFound: [],
      errorsFixed: 0
    };

    // Spinner 状态
    this.spinner = null;
    this.interactiveMode = options.interactive || false;

    // 初始化 AI Agent（延迟初始化，等待配置加载完成）
    this.agent = null;
  }

  // Spinner 管理方法
  startSpinner(text) {
    if (this.options.verbose) {
      console.log(chalk.gray(`[SPINNER] ${text}`));
      return;
    }

    if (this.spinner) {
      this.spinner.stop();
    }
    this.spinner = ora(text).start();
  }

  updateSpinner(text) {
    if (this.spinner) {
      this.spinner.text = text;
    }
  }

  succeedSpinner(text) {
    if (this.spinner) {
      this.spinner.succeed(text);
      this.spinner = null;
    }
  }

  failSpinner(text) {
    if (this.spinner) {
      this.spinner.fail(text);
      this.spinner = null;
    }
  }

  stopSpinner() {
    if (this.spinner) {
      this.spinner.stop();
      this.spinner = null;
    }
  }

  /**
   * 仅检测构建错误，不执行修复
   */
  async detectBuildErrors() {
    try {
      // 初始化
      await this.initialize();

      // 执行构建检测
      let buildResult;
      if (this.options.mode === 'dev') {
        buildResult = await this.buildExecutor.performDevCheck();
      } else {
        buildResult = await this.buildExecutor.performBuild();
      }

      return {
        success: buildResult.success,
        errors: buildResult.success ? [] : this.parseErrorsFromOutput(buildResult.output || ''),
        output: buildResult.output || ''
      };

    } catch (error) {
      return {
        success: false,
        errors: [error.message],
        output: ''
      };
    }
  }

  /**
   * 执行构建并修复错误 - 主入口方法
   */
  async buildAndFix() {
    this.buildStats.startTime = Date.now();

    try {
      this.startSpinner('开始构建项目并修复错误...');

      // 初始化
      await this.initialize();

      // 第一步：尝试 webpack codemod 迁移（在构建之前）
      this.updateSpinner('执行 webpack codemod 迁移...');
      const codemodResult = await this.performWebpackCodemodMigration();

      if (codemodResult.filesModified > 0) {
        console.log(chalk.green(`\n✅ webpack codemod 迁移完成，修改了 ${codemodResult.filesModified} 个文件`));
        this.buildStats.errorsFixed += codemodResult.filesModified;
      }

      // 根据模式执行不同的构建策略
      let buildResult;
      if (this.options.mode === 'dev') {
        this.updateSpinner('执行 dev 模式错误检测...');
        buildResult = await this.buildExecutor.performDevCheck();
      } else {
        this.updateSpinner('执行构建...');
        buildResult = await this.buildExecutor.performBuild();
      }

      if (buildResult.success) {
        this.succeedSpinner(`${this.options.mode === 'dev' ? 'Dev 检测' : '项目构建'}成功！`);
        this.buildStats.buildSuccess = true;
        this.buildStats.finalBuildSuccess = true;

        // 如果构建成功且启用了运行时错误修复，执行运行时修复
        if (this.options.enableRuntimeRepair) {
          this.updateSpinner('执行运行时错误修复...');
          const runtimeResult = await this.performRuntimeErrorRepair();

          if (runtimeResult.success) {
            this.succeedSpinner('运行时错误修复完成！');
            return this.createResultWithRuntime(true, runtimeResult);
          } else {
            console.log(chalk.yellow('⚠️  运行时错误修复失败，但构建成功'));
            return this.createResultWithRuntime(true, runtimeResult);
          }
        }

        return this.createResult(true);
      }

      // 使用 AI Agent 分析和修复错误
      this.updateSpinner('AI Agent 正在分析构建错误...');
      const fixResult = await this.analyzeAndFixWithAI(buildResult);

      this.stopSpinner();
      await this.printBuildStats();
      return fixResult;
    } catch (error) {
      this.failSpinner('构建修复过程失败');
      console.error(chalk.red('❌ 错误详情:'), error.message);
      throw error;
    } finally {
      this.buildStats.endTime = Date.now();
      this.buildStats.duration = this.buildStats.endTime - this.buildStats.startTime;
      this.stopSpinner();
    }
  }

  /**
   * 初始化构建修复器
   */
  async initialize() {
    const configFileOptions = await this.configLoader.loadConfig(
      this.userOptions.configPath,
      this.projectPath
    );

    // 合并配置：用户选项 > 配置文件 > 默认值
    this.options = {
      buildCommand: 'npm run build',
      devCommand: 'npm run dev',
      installCommand: 'npm install',
      maxAttempts: 6,
      mode: 'build',
      devTimeout: 30000,
      legacyPeerDeps: true,
      skipInstall: false,
      skipAI: false,
      dryRun: false,
      interactive: false,
      explain: false,
      verbose: false,
      maxRetries: 3,
      ...configFileOptions,
      ...this.userOptions
    };

    // 初始化构建执行器
    this.buildExecutor = new BuildExecutor(this.projectPath, {
      buildCommand: this.options.buildCommand,
      devCommand: this.options.devCommand,
      installCommand: this.options.installCommand,
      devTimeout: this.options.devTimeout,
      legacyPeerDeps: this.options.legacyPeerDeps,
      skipInstall: this.options.skipInstall,
      dryRun: this.options.dryRun,
      verbose: this.options.verbose
    });

    // 初始化 AI Agent
    this.agent = new BuildFixAgent(this.projectPath, {
      maxTokens: this.options.maxTokens || 4000,
      temperature: this.options.temperature || 0.1,
      maxRetries: this.options.maxRetries || 3,
      logDir: this.options.logDir || path.join(this.projectPath, 'ai-logs'),
      maxAttempts: this.options.maxAttempts,
      dryRun: this.options.dryRun,
      verbose: this.options.verbose
    });

    console.log(chalk.blue('🔧 构建修复器配置:'));
    console.log(chalk.gray(`  项目路径: ${this.projectPath}`));
    console.log(chalk.gray(`  构建命令: ${this.options.buildCommand}`));
    console.log(chalk.gray(`  运行模式: ${this.options.mode}`));
    console.log(chalk.gray(`  最大尝试: ${this.options.maxAttempts}`));
    console.log(chalk.gray(`  AI 修复: ${this.options.skipAI ? '禁用' : '启用'}`));
  }

  /**
   * 使用 AI Agent 分析和修复错误
   */
  async analyzeAndFixWithAI(buildResult) {
    if (this.options.skipAI) {
      console.log(chalk.yellow('⚠️  跳过 AI 修复步骤'));
      return this.createResult(false, 'AI 修复被跳过');
    }

    if (!this.agent || !this.agent.isEnabled()) {
      console.log(chalk.yellow('⚠️  AI 服务不可用，无法进行智能修复'));
      return this.createResult(false, 'AI 服务不可用');
    }

    // 收集构建错误信息
    let buildOutput = buildResult.output || '';
    console.log(chalk.blue('\n🤖 AI Agent 开始分析构建错误...'));

    // 尝试修复错误
    for (let attempt = 1; attempt <= this.options.maxAttempts; attempt++) {
      console.log(chalk.blue(`\n🔧 修复尝试 ${attempt}/${this.options.maxAttempts}...`));

      const fixResult = await this.performAIFix(buildOutput, attempt);

      if (fixResult.filesModified > 0) {
        console.log(chalk.green(`✅ AI Agent 修改了 ${fixResult.filesModified} 个文件`));
        this.buildStats.errorsFixed += fixResult.filesModified;

        // 重新构建验证修复效果
        console.log(chalk.blue('\n🔄 重新构建项目验证修复效果...'));
        const newBuildResult = await this.buildExecutor.performBuild();

        if (newBuildResult.success) {
          console.log(chalk.green('🎉 构建成功！所有错误已修复'));
          this.buildStats.finalBuildSuccess = true;
          this.displaySuccessMessage();

          // 如果构建成功且启用了运行时错误修复，执行运行时修复
          if (this.options.enableRuntimeRepair) {
            console.log(chalk.blue('\n🔧 开始运行时错误修复...'));
            const runtimeResult = await this.performRuntimeErrorRepair();
            return this.createResultWithRuntime(true, runtimeResult);
          }

          return this.createResult(true);
        } else {
          console.log(chalk.yellow('⚠️  构建仍有问题，准备下一轮修复...'));
          buildOutput = newBuildResult.output;
        }
      } else {
        console.log(chalk.yellow('⚠️  本轮 AI Agent 未能修复任何文件'));
      }
    }

    console.log(chalk.red(`❌ 经过 ${this.options.maxAttempts} 次尝试，仍无法完全修复构建错误`));
    return this.createResult(false, `AI 修复未能完全解决问题，已尝试 ${this.options.maxAttempts} 次`);
  }

  /**
   * 执行 AI 修复 - 委托给 BuildFixAgent
   */
  async performAIFix(buildOutput, attemptNumber = 1) {
    try {
      // 检查是否重复尝试相同的修复
      if (this.agent.isRepeatingAttempt(buildOutput)) {
        console.log(chalk.yellow('  ⚠️  检测到重复尝试，切换修复策略...'));
        return await this.performAlternativeStrategy(buildOutput);
      }

      // 第一阶段：让 AI 分析错误并选择要查看的文件
      console.log(chalk.gray('  📋 阶段1: AI 分析错误并选择相关文件...'));
      const analysisResult = await this.agent.analyzeBuildErrors(buildOutput, attemptNumber);

      if (!analysisResult.success) {
        console.log(chalk.yellow('  ⚠️  AI 错误分析失败'));
        return { filesModified: 0, error: analysisResult.error };
      }

      // 记录本次尝试的文件列表
      this.agent.recordAttempt(buildOutput, analysisResult.filesToFix);

      // 第二阶段：让 AI 修复选定的文件
      console.log(chalk.gray('  🔧 阶段2: AI 修复相关文件...'));
      const fixResult = await this.agent.fixFiles(analysisResult.filesToFix, buildOutput, attemptNumber);

      return fixResult;
    } catch (error) {
      console.error(chalk.red('  ❌ AI 修复过程异常:'), error.message);
      return { filesModified: 0, error: error.message };
    }
  }

  /**
   * 执行替代修复策略
   */
  async performAlternativeStrategy(buildOutput) {
    console.log(chalk.blue('  🔄 尝试替代修复策略...'));

    // 策略1: 直接删除有问题的配置
    if (buildOutput.includes('config.plugin(\'preload\').use({')) {
      console.log(chalk.gray('    📝 策略: 删除有问题的 preload 插件配置'));
      return await this.removeProblematicConfig(buildOutput);
    }

    // 策略2: 使用更简单的提示词
    console.log(chalk.gray('    📝 策略: 使用简化的修复提示词'));
    return await this.performSimplifiedFix(buildOutput);
  }

  /**
   * 删除有问题的配置
   */
  async removeProblematicConfig() {
    const vueConfigPath = path.join(this.projectPath, 'vue.config.js');

    try {
      if (await fs.pathExists(vueConfigPath)) {
        const content = await fs.readFile(vueConfigPath, 'utf8');

        // 删除有问题的 preload 插件配置
        const fixedContent = content.replace(
          /config\.plugin\('preload'\)\.use\(\{\s*\/\/[^}]*\}\)/g,
          '// Removed problematic preload plugin configuration'
        );

        if (fixedContent !== content) {
          await this.writeFileWithBackup(vueConfigPath, fixedContent);
          console.log(chalk.green('    ✅ 已删除有问题的插件配置'));
          return { filesModified: 1 };
        }
      }
    } catch (error) {
      console.log(chalk.yellow(`    ⚠️  替代策略失败: ${error.message}`));
    }

    return { filesModified: 0 };
  }

  /**
   * 执行简化修复
   */
  async performSimplifiedFix() {
    // 首先尝试 webpack codemod 迁移
    const codemodResult = await this.tryWebpackCodemodMigration();
    if (codemodResult.filesModified > 0) {
      return codemodResult;
    }

    // 使用更直接的方法，针对常见问题进行修复
    const vueConfigPath = path.join(this.projectPath, 'vue.config.js');

    try {
      if (await fs.pathExists(vueConfigPath)) {
        const content = await fs.readFile(vueConfigPath, 'utf8');
        let fixedContent = content;

        // 修复常见的 webpack 配置问题
        fixedContent = this.applyCommonFixes(fixedContent);

        if (fixedContent !== content) {
          await this.writeFileWithBackup(vueConfigPath, fixedContent);
          console.log(chalk.green('    ✅ 应用了常见修复方案'));
          return { filesModified: 1 };
        }
      }
    } catch (error) {
      console.log(chalk.yellow(`    ⚠️  简化修复失败: ${error.message}`));
    }

    return { filesModified: 0 };
  }

  /**
   * 执行 webpack codemod 迁移（主流程中的第一步）
   */
  async performWebpackCodemodMigration() {
    try {
      console.log(chalk.blue('\n🔧 检测并执行 webpack codemod 迁移...'));

      // 检测是否需要 webpack 迁移
      const detector = new WebpackConfigDetector(this.projectPath);
      const strategy = await detector.getRecommendedStrategy();

      if (!strategy.shouldMigrate) {
        console.log(chalk.gray('  ℹ️  项目无需 webpack 迁移'));
        return { filesModified: 0 };
      }

      console.log(chalk.blue(`  📋 迁移策略: ${strategy.priority} 优先级`));
      if (this.options.verbose) {
        strategy.recommendations.forEach(rec => {
          console.log(chalk.gray(`    - ${rec}`));
        });
      }

      // 执行 codemod 迁移
      const migrator = new WebpackCodemodMigrator(this.projectPath, {
        dryRun: this.options.dryRun,
        verbose: this.options.verbose,
        backup: true
      });

      const result = await migrator.migrate();

      if (result.success && result.stats.modifiedFiles > 0) {
        console.log(chalk.green(`  ✅ webpack codemod 迁移成功，修改了 ${result.stats.modifiedFiles} 个文件`));

        // 显示转换统计信息
        if (this.options.verbose && result.stats.transformations) {
          const { transformations } = result.stats;
          if (transformations.libraryTarget > 0) {
            console.log(chalk.gray(`    - Library Target 转换: ${transformations.libraryTarget} 处`));
          }
          if (transformations.targetToFalse > 0) {
            console.log(chalk.gray(`    - Target Function 转换: ${transformations.targetToFalse} 处`));
          }
          if (transformations.jsonImports > 0) {
            console.log(chalk.gray(`    - JSON Imports 转换: ${transformations.jsonImports} 处`));
          }
        }

        return { filesModified: result.stats.modifiedFiles };
      } else if (result.success) {
        console.log(chalk.gray('  ℹ️  webpack codemod 迁移完成，但无文件需要修改'));
        return { filesModified: 0 };
      } else {
        console.log(chalk.yellow(`  ⚠️  webpack codemod 迁移失败: ${result.message}`));
        return { filesModified: 0 };
      }

    } catch (error) {
      console.log(chalk.yellow(`  ⚠️  webpack codemod 迁移出错: ${error.message}`));
      if (this.options.verbose) {
        console.log(chalk.gray(`    错误详情: ${error.stack}`));
      }
      return { filesModified: 0 };
    }
  }

  /**
   * 尝试 webpack codemod 迁移（备选修复策略中使用）
   */
  async tryWebpackCodemodMigration() {
    try {
      console.log(chalk.blue('    🔧 尝试 webpack codemod 迁移...'));

      // 检测是否需要 webpack 迁移
      const detector = new WebpackConfigDetector(this.projectPath);
      const strategy = await detector.getRecommendedStrategy();

      if (!strategy.shouldMigrate) {
        console.log(chalk.gray('    ℹ️  无需 webpack 迁移'));
        return { filesModified: 0 };
      }

      console.log(chalk.blue(`    📋 迁移策略: ${strategy.priority} 优先级`));
      strategy.recommendations.forEach(rec => {
        console.log(chalk.gray(`      - ${rec}`));
      });

      // 执行 codemod 迁移
      const migrator = new WebpackCodemodMigrator(this.projectPath, {
        dryRun: this.options.dryRun,
        verbose: this.options.verbose,
        backup: true
      });

      const result = await migrator.migrate();

      if (result.success && result.stats.modifiedFiles > 0) {
        console.log(chalk.green(`    ✅ webpack codemod 迁移成功，修改了 ${result.stats.modifiedFiles} 个文件`));
        return { filesModified: result.stats.modifiedFiles };
      } else if (result.success) {
        console.log(chalk.gray('    ℹ️  webpack codemod 迁移完成，但无文件需要修改'));
        return { filesModified: 0 };
      } else {
        console.log(chalk.yellow(`    ⚠️  webpack codemod 迁移失败: ${result.message}`));
        return { filesModified: 0 };
      }

    } catch (error) {
      console.log(chalk.yellow(`    ⚠️  webpack codemod 迁移出错: ${error.message}`));
      return { filesModified: 0 };
    }
  }

  /**
   * 应用常见修复方案
   */
  applyCommonFixes(content) {
    let fixed = content;

    // 修复 preload 插件配置问题
    fixed = fixed.replace(
      /config\.plugin\('preload'\)\.use\(\{\s*\/\/[^}]*\}\)/g,
      `config.plugin('preload').tap(options => [
        {
          rel: 'preload',
          fileBlacklist: [/\\.map$/, /hot-update\\.js$/, /runtime\\..*\\.js$/],
          include: 'initial'
        }
      ])`
    );

    // 修复空的插件配置
    fixed = fixed.replace(
      /config\.plugin\([^)]+\)\.use\(\{\s*\}\)/g,
      '// Removed empty plugin configuration'
    );

    return fixed;
  }

  /**
   * 显示成功消息
   */
  displaySuccessMessage() {
    console.log(chalk.green('\n🎉 恭喜！所有构建错误已修复'));
    console.log(chalk.blue('\n💡 后续建议:'));
    console.log(chalk.gray('  1. 运行测试确保功能正常'));
    console.log(chalk.gray('  2. 检查修复后的代码是否符合预期'));
    console.log(chalk.gray('  3. 提交代码变更'));
  }

  /**
   * 创建结果对象
   */
  createResult(success, reason = null, remainingErrors = 0) {
    return {
      success,
      attempts: this.buildStats.buildAttempts,
      errorsFixed: this.buildStats.errorsFixed,
      remainingErrors,
      duration: this.buildStats.duration,
      reason
    };
  }

















  /**
   * 打印构建统计
   */
  async printBuildStats() {
    console.log('\n' + chalk.bold('🏗️  构建修复统计:'));
    console.log(`构建尝试: ${this.buildExecutor ? this.buildExecutor.getBuildAttempts() : this.buildStats.buildAttempts} 次`);
    console.log(`发现错误: ${this.buildStats.errorsFound.length} 个`);
    console.log(`修复错误: ${this.buildStats.errorsFixed} 个`);
    console.log(`最终状态: ${this.buildStats.finalBuildSuccess ? chalk.green('成功') : chalk.red('失败')}`);

    // 显示 AI Agent 统计信息
    if (this.agent) {
      const agentStats = this.agent.getFixStats();
      console.log(`AI 分析文件: ${agentStats.filesAnalyzed} 个`);
      console.log(`AI 修改文件: ${agentStats.filesModified} 个`);
      console.log(`AI 尝试次数: ${agentStats.attempts} 次`);

      // 列出所有轮次的日志文件
      await this.agent.listSessionLogs();

      // 生成会话摘要
      try {
        const summaryPath = await this.agent.generateSessionSummary();
        if (summaryPath) {
          console.log(chalk.blue(`📊 详细日志摘要: ${path.relative(this.projectPath, summaryPath)}`));
        }
      } catch (error) {
        console.warn(chalk.yellow(`⚠️  生成会话摘要失败: ${error.message}`));
      }
    }
  }

  // 为了保持向后兼容性，添加一些测试需要的方法
  detectErrorStart(errorMessage) {
    // 简单的错误类型检测
    if (errorMessage.includes('.ts(') || errorMessage.includes('error TS')) {
      return {
        type: 'typescript',
        file: errorMessage.match(/([^(]+)\(/)?.[1] || 'unknown'
      };
    }
    return { type: 'unknown', file: 'unknown' };
  }

  categorizeErrors(errors) {
    return errors.map(error => {
      if (error.category) return error;
      if (/Cannot find module|Module not found/i.test(error.message)) {
        return { ...error, category: 'missing-module' };
      }
      if (/Property \'\w+\' does not exist on type/.test(error.message)) {
        return { ...error, category: 'property-not-exist' };
      }
      if (/Vue version mismatch|Vue packages version mismatch|Vue 2 syntax is not supported/i.test(error.message)) {
        return { ...error, category: 'vue-version' };
      }
      if (error.type === 'vue') {
        return { ...error, category: 'vue' };
      }
      if (error.type === 'typescript') {
        return { ...error, category: 'typescript' };
      }
      return { ...error, category: 'unknown' };
    });
  }

  generateBuildErrorPrompt(content, error) {
    return `构建错误修复: ${error.message}`;
  }

  validateRepairedContent(repairedContent, originalContent) {
    return repairedContent !== originalContent && repairedContent.length > 0;
  }

  getBuildStats() {
    return {
      ...this.buildStats,
      buildAttempts: this.buildExecutor ? this.buildExecutor.getBuildAttempts() : this.buildStats.buildAttempts
    };
  }

  // 添加缺失的方法
  parseErrors(errorOutput) {
    const errors = [];
    const lines = errorOutput.split('\n');

    for (const line of lines) {
      // TypeScript 错误
      const tsMatch = line.match(/([^(]+)\((\d+),(\d+)\):\s*error\s+TS\d+:(.+)/);
      if (tsMatch) {
        errors.push({
          type: 'typescript',
          file: tsMatch[1],
          line: parseInt(tsMatch[2]),
          column: parseInt(tsMatch[3]),
          message: tsMatch[4].trim(),
          category: 'typescript'
        });
        continue;
      }

      // Vue 编译错误（如：src/App.vue:25:15: Template compilation error）
      const vueColonMatch = line.match(/([\w\/-]+\.vue):(\d+):(\d+):\s*(.+)/i);
      if (vueColonMatch) {
        errors.push({
          type: 'vue',
          file: vueColonMatch[1],
          line: parseInt(vueColonMatch[2]),
          column: parseInt(vueColonMatch[3]),
          message: vueColonMatch[4].trim(),
          category: 'vue'
        });
        continue;
      }

      // property-not-exist 错误
      if (/Property \'\w+\' does not exist on type/.test(line)) {
        errors.push({
          type: 'typescript',
          message: line.trim(),
          category: 'property-not-exist'
        });
        continue;
      }

      // vue-version 错误
      if (/Vue version mismatch|Vue packages version mismatch|Vue 2 syntax is not supported/i.test(line)) {
        errors.push({
          type: 'vue',
          message: line.trim(),
          category: 'vue-version'
        });
        continue;
      }

      // Webpack 错误
      if (line.includes('ERROR in') || line.includes('Module not found')) {
        errors.push({
          type: 'webpack',
          message: line.trim(),
          category: 'webpack'
        });
        continue;
      }

      // missing-module 错误
      if (/Cannot find module|Module not found/i.test(line)) {
        errors.push({
          type: 'webpack',
          message: line.trim(),
          category: 'missing-module'
        });
        continue;
      }
    }

    return errors;
  }

  getModuleMapping() {
    return this.options.moduleMapping || {};
  }



  async writeFileWithBackup(filePath, content) {
    if (this.options.dryRun) {
      console.log(chalk.gray(`[预览模式] 将写入文件: ${filePath}`));
      return;
    }

    // 备份原文件
    if (await fs.pathExists(filePath)) {
      const backupPath = `${filePath}.backup.${Date.now()}`;
      await fs.copy(filePath, backupPath);
    }

    await fs.writeFile(filePath, content, 'utf8');
  }

  /**
   * 执行运行时错误修复
   */
  async performRuntimeErrorRepair() {
    try {
      console.log(chalk.blue('🚀 启动运行时错误监控...'));

      const runtimePhase = new RuntimeErrorRepairPhase(this.projectPath, {
        timeout: this.options.runtimeTimeout,
        autoFix: this.options.runtimeAutoFix,
        verbose: this.options.verbose,
        port: this.options.runtimePort,
        devCommand: this.options.devCommand
      });

      const result = await runtimePhase.execute();

      if (result.success) {
        const { report } = result;
        console.log(chalk.green('✅ 运行时错误修复完成'));

        if (this.options.verbose && report) {
          console.log(chalk.blue('\n📊 运行时修复统计:'));
          console.log(chalk.gray(`  监控时长: ${report.summary.duration}`));
          console.log(chalk.gray(`  检测错误: ${report.summary.totalErrors} 个`));
          console.log(chalk.gray(`  修复错误: ${report.summary.fixedErrors} 个`));
          console.log(chalk.gray(`  修复成功率: ${report.summary.fixRate}`));
        }
      } else {
        console.log(chalk.yellow('⚠️  运行时错误修复失败'));
        if (this.options.verbose) {
          console.log(chalk.red(`错误: ${result.error}`));
        }
      }

      return result;

    } catch (error) {
      console.error(chalk.red('❌ 运行时错误修复异常:'), error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 创建包含运行时修复结果的结果对象
   */
  createResultWithRuntime(success, runtimeResult = null) {
    const baseResult = this.createResult(success);

    if (runtimeResult) {
      baseResult.runtimeRepair = {
        success: runtimeResult.success,
        report: runtimeResult.report || null,
        stats: runtimeResult.stats || null
      };

      // 如果有运行时修复统计，添加到总体统计中
      if (runtimeResult.stats) {
        baseResult.runtimeErrorsDetected = runtimeResult.stats.totalErrors || 0;
        baseResult.runtimeErrorsFixed = runtimeResult.stats.fixedErrors || 0;
      }
    }

    return baseResult;
  }
}

module.exports = BuildFixer;
