const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { AIService } = require('./ai-service');
const ToolExecutor = require('../services/tools/ToolExecutor');
const PromptBuilder = require('../services/ai/PromptBuilder');
const ErrorAnalyzer = require('./ErrorAnalyzer');

/**
 * BuildFixAgent - AI驱动的构建错误修复代理（重构版）
 *
 * 专门负责：
 * - AI调用和错误分析
 * - 工具执行协调
 * - 错误分类和修复策略
 * - 与BuildFixer协作完成修复任务
 */
class BuildFixAgent extends AIService {
  constructor(projectPath, options = {}) {
    const aiOptions = {
      ...options,
      logDir: options.logDir || path.join(projectPath || process.cwd(), 'ai-logs')
    };
    super(aiOptions);

    this.projectPath = projectPath;
    this.options = {
      maxAttempts: 10,
      dryRun: false,
      verbose: false,
      ...options
    };

    // 初始化重构后的工具执行器
    this.toolExecutor = new ToolExecutor(projectPath, this.options);

    // 初始化提示词构建器
    this.promptBuilder = new PromptBuilder(this.toolExecutor.getToolRegistry(), this.options);

    // 初始化错误分析器
    this.errorAnalyzer = new ErrorAnalyzer(projectPath, this, this.toolExecutor, this.options);
    
    // 设置ErrorAnalyzer的PromptBuilder
    this.errorAnalyzer.setPromptBuilder(this.promptBuilder);

    // 重复检测
    this.attemptHistory = {
      filesToFix: [], // 记录每次尝试修复的文件列表
      errorHashes: [], // 记录每次的错误哈希
      lastErrorOutput: null // 记录上次的错误输出
    };

    // 修复统计
    this.fixStats = {
      filesAnalyzed: 0,
      filesModified: 0,
      errorsFixed: 0,
      attempts: 0
    };
  }

  /**
   * 分析构建错误并选择需要修复的文件
   */
  async analyzeBuildErrors(buildOutput, attemptNumber = 1) {
    return await this.errorAnalyzer.analyzeBuildErrors(buildOutput, attemptNumber);
  }

  /**
   * 修复指定的文件列表
   */
  async fixFiles(filesToFix, buildOutput, attemptNumber = 1) {
    let filesModified = 0;
    const errors = [];

    // 获取之前的尝试记录
    const previousAttempts = this.getPreviousAttempts(filesToFix, attemptNumber);

    for (let fileIndex = 0; fileIndex < filesToFix.length; fileIndex++) {
      const filePath = filesToFix[fileIndex];
      try {
        console.log(chalk.gray(`🔧 修复文件: ${filePath}`));

        // 读取文件内容
        const fileResult = await this.toolExecutor.executeToolCall('read_file', { file_path: filePath });

        if (!fileResult.success) {
          console.log(chalk.yellow(`  ⚠️  无法读取文件: ${fileResult.error}`));
          errors.push(`无法读取文件 ${filePath}: ${fileResult.error}`);
          continue;
        }

        // 获取该文件的之前尝试记录
        const filePreviousAttempts = previousAttempts.filter(attempt => attempt.filePath === filePath);

        // 让 AI 修复文件，添加详细的上下文信息
        const fixResult = await this.fixSingleFile(
          filePath,
          fileResult.content,
          buildOutput,
          attemptNumber,
          fileIndex + 1,
          filesToFix.length,
          filePreviousAttempts
        );

        if (fixResult.success) {
          // 写入修复后的文件
          const writeResult = await this.toolExecutor.executeToolCall('write_file', {
            file_path: filePath,
            content: fixResult.fixedContent
          });

          if (writeResult.success) {
            console.log(chalk.green('  ✅ 文件修复成功'));
            filesModified++;
            this.fixStats.filesModified++;
          } else {
            console.log(chalk.yellow(`  ⚠️  无法写入文件: ${writeResult.error}`));
            errors.push(`无法写入文件 ${filePath}: ${writeResult.error}`);
          }
        } else {
          console.log(chalk.yellow(`  ⚠️  AI 修复失败: ${fixResult.error}`));

          // 显示 AI 响应的部分内容用于调试
          if (fixResult.aiResponse) {
            console.log(chalk.gray(`     AI 响应预览: ${fixResult.aiResponse}`));
          }

          errors.push(`AI 修复失败 ${filePath}: ${fixResult.error}`);

          // 记录失败的尝试
          this.recordFailedAttempt(filePath, attemptNumber, fixResult.error);
        }
      } catch (error) {
        console.log(chalk.red(`  ❌ 修复文件异常: ${error.message}`));
        errors.push(`修复文件异常 ${filePath}: ${error.message}`);
        this.recordFailedAttempt(filePath, attemptNumber, error.message);
      }
    }

    this.fixStats.attempts++;
    this.fixStats.filesAnalyzed += filesToFix.length;

    return {
      success: filesModified > 0,
      filesModified,
      errors: errors.length > 0 ? errors : undefined,
      totalFiles: filesToFix.length
    };
  }

  /**
   * 执行工具调用（委托给ToolExecutor）
   */
  async executeToolCall(toolName, parameters) {
    return await this.toolExecutor.executeToolCall(toolName, parameters);
  }

  /**
   * 修复单个文件 - 两轮AI调用模式
   * 第一轮：根据错误生成工具调用，决定需要读取哪些文件
   * 第二轮：基于读取的文件内容和错误信息，生成具体的修复代码
   */
  async fixSingleFile(filePath, fileContent, buildOutput, attemptNumber = 1, fileIndex = 1, totalFiles = 1, previousAttempts = []) {
    try {
      // 第一轮：生成工具调用
      const toolCalls = await this.generateToolCalls(filePath, buildOutput, attemptNumber, previousAttempts);

      if (!toolCalls || toolCalls.length === 0) {
        // 如果没有工具调用，直接进行修复
        return await this.generateFileFix(
          filePath, fileContent, buildOutput, {},
          attemptNumber, fileIndex, totalFiles, previousAttempts
        );
      }

      // 执行工具调用，收集相关文件内容
      const contextFiles = await this.executeToolCalls(toolCalls);

      // 第二轮：基于收集的文件内容生成修复代码
      return await this.generateFileFix(
        filePath, fileContent, buildOutput, contextFiles,
        attemptNumber, fileIndex, totalFiles, previousAttempts
      );
    } catch (error) {
      return {
        success: false,
        error: error.message,
        filePath
      };
    }
  }

  /**
   * 修复运行时错误
   * 专门处理Vue运行时错误，包括组件错误、响应式数据问题等
   */
  async fixRuntimeError(errorContext) {
    try {
      const { fileName, message, stack, componentTrace, buildOutput } = errorContext;

      if (this.options.verbose) {
        console.log(chalk.gray(`🔧 开始修复运行时错误: ${fileName}`));
      }

      // 读取出错文件内容
      const filePath = path.resolve(this.projectPath, fileName);
      if (!await fs.pathExists(filePath)) {
        throw new Error(`文件不存在: ${fileName}`);
      }

      const fileContent = await fs.readFile(filePath, 'utf8');

      // 使用专门的运行时错误修复流程
      const fixResult = await this.fixRuntimeErrorFile(
        fileName, fileContent, errorContext
      );

      if (this.options.verbose) {
        console.log(chalk.gray(`       🔍 修复结果: success=${fixResult.success}, hasNewContent=${!!fixResult.newContent}, hasFixedContent=${!!fixResult.fixedContent}`));
      }

      // 检查修复结果
      const fixedContent = fixResult.newContent || fixResult.fixedContent;

      if (fixResult.success && fixedContent) {
        // 应用修复
        if (!this.options.dryRun) {
          await fs.writeFile(filePath, fixedContent, 'utf8');
          console.log(chalk.green(`✅ 运行时错误修复已应用: ${fileName}`));
        } else {
          console.log(chalk.blue(`🔍 [DRY RUN] 运行时错误修复预览: ${fileName}`));
        }

        this.fixStats.filesModified++;
        this.fixStats.errorsFixed++;

        return {
          success: true,
          fixedContent: fixedContent,
          newContent: fixedContent
        };
      } else {
        if (this.options.verbose) {
          console.log(chalk.gray(`       ❌ 修复失败: ${fixResult.error || '未知错误'}`));
        }
        return fixResult;
      }

    } catch (error) {
      console.error(chalk.red(`运行时错误修复失败: ${error.message}`));
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 修复运行时错误文件
   */
  async fixRuntimeErrorFile(fileName, fileContent, errorContext) {
    try {
      const context = {
        taskType: 'runtime-error-fix',
        errorType: errorContext.type || 'runtime',
        fileName,
        message: errorContext.message,
        stack: errorContext.stack,
        componentTrace: errorContext.componentTrace
      };

      const prompt = this.promptBuilder.buildRuntimeErrorFixPrompt(
        fileName, fileContent, errorContext, context
      );

      const response = await this.callAI(prompt, {
        context: {
          taskType: 'runtime-error-fix',
          phase: 'fix-generation',
          fileName: fileName,
          errorType: errorContext.type
        }
      });

      return this.parseFixResponse(response, fileContent);
    } catch (error) {
      return {
        success: false,
        error: error.message,
        aiResponse: error.response || null
      };
    }
  }

  /**
   * 第一轮AI调用：根据构建错误生成工具调用
   */
  async generateToolCalls(filePath, buildOutput, attemptNumber = 1, previousAttempts = []) {
    try {
      const context = {
        attemptNumber,
        previousAttempts,
        taskType: 'tool-call-generation'
      };

      const prompt = this.promptBuilder.buildToolCallPrompt(filePath, buildOutput, context);
      
      const response = await this.callAI(prompt, {
        context: {
          taskType: 'tool-call-generation',
          attemptNumber: attemptNumber,
          phase: 'tool-calls',
          fileName: filePath
        }
      });

      return this.parseToolCallsResponse(response);
    } catch (error) {
      console.log(chalk.yellow(`⚠️  生成工具调用失败: ${error.message}`));
      return [];
    }
  }

  /**
   * 执行工具调用，收集相关文件内容
   */
  async executeToolCalls(toolCalls) {
    try {
      const result = await this.toolExecutor.executeToolCalls(toolCalls);
      
      if (result.success) {
        return this.toolExecutor.formatContextFiles(result.results);
      } else {
        console.log(chalk.yellow(`⚠️  工具调用执行失败: ${result.errors?.join(', ')}`));
        return {};
      }
    } catch (error) {
      console.log(chalk.yellow(`⚠️  工具调用执行异常: ${error.message}`));
      return {};
    }
  }

  /**
   * 第二轮AI调用：基于收集的文件内容生成修复代码
   */
  async generateFileFix(filePath, fileContent, buildOutput, contextFiles, attemptNumber = 1, fileIndex = 1, totalFiles = 1, previousAttempts = []) {
    try {
      const context = {
        attemptNumber,
        fileIndex,
        totalFiles,
        previousAttempts,
        taskType: 'file-fix'
      };

      const prompt = this.promptBuilder.buildFileFixPromptWithContext(
        filePath, fileContent, buildOutput, contextFiles, context
      );
      
      const response = await this.callAI(prompt, {
        context: {
          taskType: 'file-fix',
          attemptNumber: attemptNumber,
          phase: 'fix-generation',
          fileName: filePath
        }
      });

      return this.parseFixResponse(response, fileContent);
    } catch (error) {
      return {
        success: false,
        error: error.message,
        aiResponse: error.response || null
      };
    }
  }

  /**
   * 解析修复响应
   */
  parseFixResponse(response, originalContent) {
    try {
      if (this.options.verbose) {
        console.log(chalk.gray(`    🔍 开始解析AI响应...`));
        console.log(chalk.gray(`       响应长度: ${response.length} 字符`));
        console.log(chalk.gray(`       包含<file_fix>: ${response.includes('<file_fix>')}`));
        console.log(chalk.gray(`       包含</file_fix>: ${response.includes('</file_fix>')}`));
        console.log(chalk.gray(`       包含<content>: ${response.includes('<content>')}`));
        console.log(chalk.gray(`       包含</content>: ${response.includes('</content>')}`));
        console.log(chalk.gray(`       包含<fix_result>: ${response.includes('<fix_result>')}`));
        console.log(chalk.gray(`       包含</fix_result>: ${response.includes('</fix_result>')}`));
        console.log(chalk.gray(`       包含<fixed_content>: ${response.includes('<fixed_content>')}`));
        console.log(chalk.gray(`       包含</fixed_content>: ${response.includes('</fixed_content>')}`));

        // 显示响应的前500字符用于调试
        console.log(chalk.gray(`       响应前500字符: ${response.substring(0, 500)}`));
      }

      // 尝试解析运行时错误修复的特殊格式（优先级最高）
      if (this.options.verbose) {
        console.log(chalk.gray(`       🔍 尝试解析运行时修复格式...`));
      }

      const runtimeFixMatch = response.match(/<fix_result>[\s\S]*?<fixed_content>([\s\S]*?)<\/fixed_content>[\s\S]*?<\/fix_result>/);

      if (runtimeFixMatch) {
        let content = runtimeFixMatch[1].trim();

        if (this.options.verbose) {
          console.log(chalk.gray(`       🔍 运行时修复格式原始内容: ${content.substring(0, 200)}...`));
        }

        // 解码 HTML 实体
        content = this.decodeHtmlEntities(content);

        if (this.options.verbose) {
          console.log(chalk.gray(`       ✅ 运行时修复格式解析成功，内容长度: ${content.length} 字符`));
          console.log(chalk.gray(`       解码后内容前100字符: ${content.substring(0, 100)}`));
          console.log(chalk.gray(`       包含<template>: ${content.includes('<template>')}`));
          console.log(chalk.gray(`       包含<script>: ${content.includes('<script>')}`));
        }

        if (content) {
          const validationResult = this.validateFixedContent(content, originalContent);
          if (validationResult) {
            return {
              success: true,
              newContent: validationResult,
              fixedContent: validationResult
            };
          }
        }
      } else {
        if (this.options.verbose) {
          console.log(chalk.gray(`       ❌ 运行时修复格式解析失败`));
        }
      }

      // 尝试解析 XML 格式
      const xmlMatch = response.match(/<file_fix>[\s\S]*?<content>([\s\S]*?)<\/content>[\s\S]*?<\/file_fix>/);

      if (xmlMatch) {
        let content = xmlMatch[1].trim();

        // 解码 HTML 实体
        content = this.decodeHtmlEntities(content);

        if (this.options.verbose) {
          console.log(chalk.gray(`       ✅ XML解析成功，内容长度: ${content.length} 字符`));
          console.log(chalk.gray(`       内容前100字符: ${content.substring(0, 100)}`));
        }

        if (content) {
          const validationResult = this.validateFixedContent(content, originalContent);
          if (validationResult) {
            return {
              success: true,
              newContent: validationResult,
              fixedContent: validationResult
            };
          }
        }
      } else {
        if (this.options.verbose) {
          console.log(chalk.gray(`       ❌ XML解析失败，尝试其他格式...`));
        }
      }

      // 回退：尝试解析代码块格式
      const codeBlockMatch = response.match(/```(?:vue|js|ts|javascript|typescript|xml)?\s*([\s\S]*?)\s*```/);

      if (codeBlockMatch) {
        let content = codeBlockMatch[1].trim();

        if (this.options.verbose) {
          console.log(chalk.gray(`       🔍 尝试解析代码块格式...`));
          console.log(chalk.gray(`       代码块内容前200字符: ${content.substring(0, 200)}`));
        }

        // 检查代码块内容是否包含运行时修复格式
        const innerRuntimeFixMatch = content.match(/<fix_result>[\s\S]*?<fixed_content>([\s\S]*?)<\/fixed_content>[\s\S]*?<\/fix_result>/);

        if (innerRuntimeFixMatch) {
          content = innerRuntimeFixMatch[1].trim();

          if (this.options.verbose) {
            console.log(chalk.gray(`       ✅ 在代码块中找到运行时修复格式`));
            console.log(chalk.gray(`       运行时修复内容前200字符: ${content.substring(0, 200)}`));
          }

          // 解码 HTML 实体
          content = this.decodeHtmlEntities(content);

          if (this.options.verbose) {
            console.log(chalk.gray(`       解码后内容前100字符: ${content.substring(0, 100)}`));
            console.log(chalk.gray(`       包含<template>: ${content.includes('<template>')}`));
            console.log(chalk.gray(`       包含<script>: ${content.includes('<script')}`));
          }
        } else {
          if (this.options.verbose) {
            console.log(chalk.gray(`       ✅ 标准代码块解析成功，内容长度: ${content.length} 字符`));
          }
        }

        if (content) {
          const validationResult = this.validateFixedContent(content, originalContent);
          if (validationResult) {
            return {
              success: true,
              newContent: validationResult,
              fixedContent: validationResult
            };
          }
        }
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`       ❌ 所有解析方法都失败了`));
      }

      return {
        success: false,
        error: '无法解析AI响应格式'
      };
    } catch (error) {
      console.warn(chalk.yellow('⚠️  解析 AI 修复响应失败'));
      if (this.options.verbose) {
        console.log(chalk.gray(`       错误详情: ${error.message}`));
      }
      return {
        success: false,
        error: `解析修复响应失败: ${error.message}`
      };
    }
  }

  /**
   * 解码 HTML 实体
   */
  decodeHtmlEntities(text) {
    const entities = {
      '&lt;': '<',
      '&gt;': '>',
      '&amp;': '&',
      '&quot;': '"',
      '&#39;': "'",
      '&apos;': "'"
    };

    return text.replace(/&[a-zA-Z0-9#]+;/g, (entity) => {
      return entities[entity] || entity;
    });
  }

  /**
   * 验证修复后的内容
   */
  validateFixedContent(fixedContent, originalContent) {
    if (!fixedContent) {
      if (this.options.verbose) {
        console.log(chalk.gray('       ❌ 修复内容为空'));
      }
      return null;
    }

    // 标准化内容进行比较（移除多余空格和换行）
    const normalizeContent = (content) => {
      return content
        .replace(/\r\n/g, '\n') // 统一换行符
        .replace(/\s+$/gm, '') // 移除行尾空格
        .trim(); // 移除首尾空格
    };

    const normalizedFixed = normalizeContent(fixedContent);
    const normalizedOriginal = normalizeContent(originalContent);

    if (this.options.verbose) {
      console.log(chalk.gray(`       🔍 内容验证: 原文件${normalizedOriginal.length}字符, 修复后${normalizedFixed.length}字符`));
    }

    // 如果内容完全相同，检查是否是有意的"无需修复"响应
    if (normalizedFixed === normalizedOriginal) {
      // 检查响应中是否包含"无需修复"的指示
      const noFixIndicators = [
        '无需修复',
        'no fix needed',
        'already correct',
        '已经正确',
        'file is correct'
      ];

      // 如果 AI 明确表示无需修复，则返回原内容（表示处理成功）
      // 否则返回 null（表示修复失败）
      const hasNoFixIndicator = noFixIndicators.some(indicator =>
        fixedContent.toLowerCase().includes(indicator.toLowerCase())
      );

      if (hasNoFixIndicator) {
        if (this.options.verbose) {
          console.log(chalk.gray('       ℹ️  AI 判断文件无需修复'));
        }
        return originalContent;
      } else {
        if (this.options.verbose) {
          console.log(chalk.gray('       ⚠️  AI 返回的内容与原文件相同，可能修复失败'));
        }
        return null;
      }
    }

    // 基本的内容有效性检查
    if (normalizedFixed.length < 10) {
      if (this.options.verbose) {
        console.log(chalk.gray('       ⚠️  修复后的内容过短，可能无效'));
      }
      return null;
    }

    // 检查是否包含基本的文件结构（针对不同文件类型）
    const hasValidStructure = this.validateFileStructure(fixedContent, originalContent);
    if (!hasValidStructure) {
      if (this.options.verbose) {
        console.log(chalk.gray('       ⚠️  修复后的内容缺少必要的文件结构'));
      }
      return null;
    }

    if (this.options.verbose) {
      console.log(chalk.gray('       ✅ 内容验证通过'));
    }

    return fixedContent;
  }

  /**
   * 验证文件结构的有效性
   */
  validateFileStructure(fixedContent, originalContent) {
    if (this.options.verbose) {
      console.log(chalk.gray(`    🔍 验证文件结构...`));
      console.log(chalk.gray(`       原文件长度: ${originalContent.length} 字符`));
      console.log(chalk.gray(`       修复后长度: ${fixedContent.length} 字符`));
    }

    // 对于 Vue 文件，检查基本结构
    if (originalContent.includes('<template>') || originalContent.includes('<script')) {
      const hasTemplate = fixedContent.includes('<template>') || !originalContent.includes('<template>');
      // 检查各种 script 标签格式
      const hasScript = fixedContent.includes('<script') || !originalContent.includes('<script');

      if (this.options.verbose) {
        console.log(chalk.gray(`       Vue文件检查: template=${hasTemplate}, script=${hasScript}`));
        console.log(chalk.gray(`       原文件包含template: ${originalContent.includes('<template>')}`));
        console.log(chalk.gray(`       原文件包含script: ${originalContent.includes('<script')}`));
        console.log(chalk.gray(`       修复后包含template: ${fixedContent.includes('<template>')}`));
        console.log(chalk.gray(`       修复后包含script: ${fixedContent.includes('<script')}`));
        console.log(chalk.gray(`       修复后包含script setup: ${fixedContent.includes('<script setup')}`));
        console.log(chalk.gray(`       修复后包含script setup lang: ${fixedContent.includes('<script setup lang')}`));
      }

      // 对于Vue文件，如果AI只修复了样式部分，可能只返回template部分
      // 这种情况下，我们应该接受部分修复
      const isPartialFix = fixedContent.includes('<template>') && !fixedContent.includes('<script') && originalContent.includes('<script');

      if (isPartialFix) {
        if (this.options.verbose) {
          console.log(chalk.gray(`       检测到部分修复（仅template），接受修复`));
        }
        return true;
      }

      return hasTemplate && hasScript;
    }

    // 对于 JS/TS 文件，检查基本的导入导出结构
    // 首先检查文件扩展名，避免误判SCSS等文件
    const hasJSExtension = /\.(js|ts|jsx|tsx)$/i.test(originalContent);
    const hasJSContent = originalContent.includes('export') ||
                        originalContent.includes('module.exports') ||
                        originalContent.includes('import ') ||
                        originalContent.includes('require(');

    // 排除SCSS/CSS文件
    const isCSSFile = originalContent.includes('@use') ||
                     originalContent.includes('@import') ||
                     originalContent.includes('@mixin') ||
                     /\.(scss|sass|css)$/i.test(originalContent);

    const isJSFile = (hasJSExtension || hasJSContent) && !isCSSFile;

    if (isJSFile) {
      const hasExport = fixedContent.includes('export') || fixedContent.includes('module.exports');

      if (this.options.verbose) {
        console.log(chalk.gray(`       JS/TS文件检查: export=${hasExport}`));
      }

      return hasExport;
    }

    // 对于 SCSS/CSS 文件，检查基本的样式结构
    if (originalContent.includes('{') && originalContent.includes('}')) {
      const openBraces = (fixedContent.match(/\{/g) || []).length;
      const closeBraces = (fixedContent.match(/\}/g) || []).length;
      const isValid = openBraces === closeBraces && openBraces > 0;

      if (this.options.verbose) {
        console.log(chalk.gray(`       CSS/SCSS文件检查: 开括号=${openBraces}, 闭括号=${closeBraces}, 有效=${isValid}`));
      }

      return isValid;
    }

    // 对于其他文件类型，默认认为有效
    if (this.options.verbose) {
      console.log(chalk.gray(`       其他文件类型，默认有效`));
    }

    return true;
  }

  /**
   * 生成重试上下文信息
   */
  generateRetryContext(attemptNumber, previousAttempts) {
    if (attemptNumber === 1 || previousAttempts.length === 0) {
      return '';
    }

    let retryContext = `\n\n**🔄 重试信息 (第 ${attemptNumber} 次尝试)**：\n`;
    retryContext += `之前已经尝试了 ${previousAttempts.length} 次修复，但都没有成功。请仔细分析之前的失败原因，采用不同的修复策略。\n\n`;

    // 分析之前的尝试
    if (previousAttempts.length > 0) {
      retryContext += '**之前尝试的问题分析**：\n';

      previousAttempts.forEach((attempt, index) => {
        retryContext += `- 尝试 ${index + 1}: ${attempt.error || '修复失败'}\n`;

        if (attempt.approach) {
          retryContext += `  采用方法: ${attempt.approach}\n`;
        }
      });

      retryContext += '\n**本次修复策略建议**：\n';

      if (attemptNumber === 2) {
        retryContext += '- 🎯 **更激进的修复**: 不要只修复明显的错误，考虑重构整个文件结构\n';
        retryContext += '- 🔍 **深入分析**: 检查依赖关系、导入路径、配置兼容性\n';
        retryContext += '- 📝 **完整重写**: 如果小修小补不行，考虑按照 Vue 3 最佳实践重写\n';
      } else if (attemptNumber === 3) {
        retryContext += '- 🚀 **彻底重构**: 完全按照 Vue 3 + Element Plus 的标准重写文件\n';
        retryContext += '- 🎨 **现代化语法**: 使用最新的 ES6+、Composition API、TypeScript 语法\n';
        retryContext += '- 🔧 **工具链适配**: 确保与最新的构建工具和依赖版本兼容\n';
      } else {
        retryContext += '- 💡 **创新方案**: 尝试完全不同的实现方式\n';
        retryContext += '- 🔬 **细致检查**: 逐行检查每个语法细节\n';
        retryContext += '- 📚 **参考最佳实践**: 查阅官方文档和社区最佳实践\n';
      }
    }

    return retryContext;
  }

  /**
   * 获取之前的尝试记录
   */
  getPreviousAttempts(filesToFix, currentAttemptNumber) {
    const previousAttempts = [];

    // 从历史记录中获取之前的尝试
    for (let i = 1; i < currentAttemptNumber; i++) {
      const attemptKey = `attempt_${i}`;
      if (this.attemptHistory[attemptKey]) {
        this.attemptHistory[attemptKey].forEach(record => {
          if (filesToFix.includes(record.filePath)) {
            previousAttempts.push({
              attemptNumber: i,
              filePath: record.filePath,
              error: record.error,
              approach: record.approach || this.inferApproachFromError(record.error)
            });
          }
        });
      }
    }

    return previousAttempts;
  }

  /**
   * 记录失败的尝试
   */
  recordFailedAttempt(filePath, attemptNumber, error) {
    const attemptKey = `attempt_${attemptNumber}`;

    if (!this.attemptHistory[attemptKey]) {
      this.attemptHistory[attemptKey] = [];
    }

    this.attemptHistory[attemptKey].push({
      filePath,
      error,
      timestamp: new Date().toISOString(),
      approach: this.inferApproachFromError(error)
    });
  }

  /**
   * 从错误信息推断修复方法
   */
  inferApproachFromError(error) {
    if (!error || typeof error !== 'string') {
      return '未知方法';
    }

    const lowerError = error.toLowerCase();

    if (lowerError.includes('内容与原文件相同')) {
      return '小幅修改';
    } else if (lowerError.includes('缺少必要的文件结构')) {
      return '结构修复';
    } else if (lowerError.includes('内容过短')) {
      return '内容补充';
    } else if (lowerError.includes('语法错误') || lowerError.includes('syntax error')) {
      return '语法错误修复';
    } else if (lowerError.includes('导入') || lowerError.includes('import')) {
      return '导入语句修复';
    } else {
      return '通用修复';
    }
  }

  /**
   * 显示 AI 响应摘要
   */
  displayAIResponseSummary(response, filePath, attemptNumber) {
    if (!response || typeof response !== 'string') {
      console.log(chalk.yellow('  ⚠️  AI 响应为空或无效'));
      return;
    }

    const fileName = path.basename(filePath);
    const responseLength = response.length;

    console.log(chalk.cyan(`  📄 AI 响应摘要 (${fileName}, 尝试 ${attemptNumber}):`));
    console.log(chalk.gray(`     响应长度: ${responseLength} 字符`));

    // 检查响应中是否包含 XML 标签
    const hasXMLTags = response.includes('<file_fix>') && response.includes('</file_fix>');
    const hasCodeBlocks = response.includes('```');

    if (hasXMLTags) {
      console.log(chalk.green('     ✅ 包含 XML 格式的修复内容'));

      // 提取并显示修复说明
      const explanationMatch = response.match(/<file_fix>([\s\S]*?)<content>/);
      if (explanationMatch) {
        const explanation = explanationMatch[1].trim();
        if (explanation && explanation.length > 10) {
          const shortExplanation = explanation.length > 100
            ? explanation.substring(0, 100) + '...'
            : explanation;
          console.log(chalk.gray(`     说明: ${shortExplanation}`));
        }
      }
    } else if (hasCodeBlocks) {
      console.log(chalk.yellow('     ⚠️  包含代码块格式（非标准 XML）'));
    } else {
      console.log(chalk.red('     ❌ 未检测到有效的修复格式'));
    }

    // 显示响应的前几行作为预览
    const lines = response.split('\n').slice(0, 3);
    const preview = lines.join('\n').substring(0, 150);
    if (preview.trim()) {
      console.log(chalk.gray(`     预览: ${preview.trim()}${response.length > 150 ? '...' : ''}`));
    }
  }

  /**
   * 生成修复会话摘要
   */
  async generateSessionSummary() {
    try {
      const sessionId = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
      const summaryPath = path.join(this.options.logDir, `session-summary-${sessionId}.json`);

      // 读取所有相关的日志文件
      const logFiles = await this.getSessionLogFiles();
      const sessionData = {
        sessionId: sessionId,
        projectPath: this.projectPath,
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        totalAttempts: this.fixStats.attempts,
        filesAnalyzed: this.fixStats.filesAnalyzed,
        filesModified: this.fixStats.filesModified,
        attempts: []
      };

      // 按轮次组织日志数据
      for (const logFile of logFiles) {
        try {
          const logData = await fs.readJson(logFile);
          const attemptNumber = logData.context?.attemptNumber || 1;

          if (!sessionData.attempts[attemptNumber - 1]) {
            sessionData.attempts[attemptNumber - 1] = {
              attemptNumber: attemptNumber,
              phases: []
            };
          }

          sessionData.attempts[attemptNumber - 1].phases.push({
            phase: logData.context?.phase || 'unknown',
            taskType: logData.context?.taskType || 'unknown',
            fileName: logData.context?.fileName || 'unknown',
            success: logData.success,
            duration_ms: logData.duration_ms,
            timestamp: logData.timestamp,
            logFile: path.basename(logFile)
          });
        } catch (error) {
          console.warn(chalk.yellow(`⚠️  无法读取日志文件: ${logFile}`));
        }
      }

      // 写入会话摘要
      await fs.writeJson(summaryPath, sessionData, { spaces: 2 });
      console.log(chalk.blue(`📊 会话摘要已生成: ${path.basename(summaryPath)}`));

      return summaryPath;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  生成会话摘要失败: ${error.message}`));
      return null;
    }
  }

  /**
   * 获取会话相关的日志文件
   */
  async getSessionLogFiles() {
    try {
      // 确保日志目录存在
      await fs.ensureDir(this.options.logDir);

      const files = await fs.readdir(this.options.logDir);
      const logFiles = files
        .filter(file => file.endsWith('.json') && file.includes('ai-call'))
        .map(file => path.join(this.options.logDir, file))
        .sort();

      console.log(chalk.gray(`📁 找到 ${logFiles.length} 个 AI 调用日志文件`));

      // 显示最新的几个日志文件
      if (logFiles.length > 0) {
        const recentFiles = logFiles.slice(-3);
        console.log(chalk.gray('   最新日志文件:'));
        recentFiles.forEach(file => {
          console.log(chalk.gray(`   - ${path.basename(file)}`));
        });
      }

      return logFiles;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  无法读取日志目录 ${this.options.logDir}: ${error.message}`));

      // 尝试创建日志目录
      try {
        await fs.ensureDir(this.options.logDir);
        console.log(chalk.green(`✅ 已创建日志目录: ${this.options.logDir}`));
        return [];
      } catch (createError) {
        console.error(chalk.red(`❌ 无法创建日志目录: ${createError.message}`));
        return [];
      }
    }
  }

  /**
   * 获取修复统计信息
   */
  getFixStats() {
    return {
      ...this.fixStats,
      aiStats: this.getStats()
    };
  }

  /**
   * 重置修复统计
   */
  resetFixStats() {
    this.fixStats = {
      filesAnalyzed: 0,
      filesModified: 0,
      errorsFixed: 0,
      attempts: 0
    };
    this.resetStats();
  }

  /**
   * 列出所有轮次的日志文件
   */
  async listSessionLogs() {
    try {
      const files = await fs.readdir(this.options.logDir);
      const logFiles = files
        .filter(file => file.endsWith('.json') && file.includes('ai-call'))
        .sort();

      if (logFiles.length === 0) {
        console.log(chalk.gray('📝 没有找到 AI 调用日志文件'));
        return [];
      }

      console.log(chalk.blue(`📝 找到 ${logFiles.length} 个 AI 调用日志文件:`));

      // 按轮次分组显示
      const attempts = {};
      for (const file of logFiles) {
        const match = file.match(/attempt(\d+)/);
        if (match) {
          const attemptNum = parseInt(match[1]);
          if (!attempts[attemptNum]) {
            attempts[attemptNum] = [];
          }
          attempts[attemptNum].push(file);
        }
      }

      // 显示每个轮次的日志
      Object.keys(attempts).sort((a, b) => parseInt(a) - parseInt(b)).forEach(attemptNum => {
        console.log(chalk.gray(`\n  轮次 ${attemptNum}:`));
        attempts[attemptNum].forEach(file => {
          console.log(chalk.gray(`    - ${file}`));
        });
      });

      return logFiles;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  无法列出日志文件: ${error.message}`));
      return [];
    }
  }

  /**
   * 检查是否重复尝试相同的修复
   */
  isRepeatingAttempt(buildOutput) {
    // 生成当前错误的哈希
    const currentErrorHash = this.errorAnalyzer.generateErrorHash(buildOutput);

    // 检查是否已经尝试过相同的错误
    if (this.attemptHistory.errorHashes.includes(currentErrorHash)) {
      return true;
    }

    // 检查错误输出是否与上次完全相同
    if (this.attemptHistory.lastErrorOutput === buildOutput) {
      return true;
    }

    return false;
  }

  /**
   * 记录本次尝试
   */
  recordAttempt(buildOutput, filesToFix) {
    const errorHash = this.errorAnalyzer.generateErrorHash(buildOutput);

    this.attemptHistory.errorHashes.push(errorHash);
    this.attemptHistory.filesToFix.push([...filesToFix]);
    this.attemptHistory.lastErrorOutput = buildOutput;

    // 只保留最近3次尝试的记录
    if (this.attemptHistory.errorHashes.length > 3) {
      this.attemptHistory.errorHashes.shift();
      this.attemptHistory.filesToFix.shift();
    }
  }

  /**
   * 解析工具调用响应
   */
  parseToolCallsResponse(response) {
    try {
      // 尝试解析JSON格式
      const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        const jsonData = JSON.parse(jsonMatch[1]);
        if (jsonData.tool_calls && Array.isArray(jsonData.tool_calls)) {
          return jsonData.tool_calls;
        }
      }

      // 尝试解析XML格式（向后兼容）
      const xmlMatch = response.match(/<tool_calls>([\s\S]*?)<\/tool_calls>/);
      if (xmlMatch) {
        const toolCalls = [];
        const callsSection = xmlMatch[1];
        const callMatches = callsSection.match(/<call>([\s\S]*?)<\/call>/g);

        if (callMatches) {
          for (const callMatch of callMatches) {
            const nameMatch = callMatch.match(/<n>(.*?)<\/n>/);
            const parametersMatch = callMatch.match(/<parameters>([\s\S]*?)<\/parameters>/);

            if (nameMatch && parametersMatch) {
              const toolName = nameMatch[1].trim();
              const parameters = {};

              // 解析参数
              const paramSection = parametersMatch[1];
              const paramMatches = paramSection.match(/<(\w+)>(.*?)<\/\w+>/g);

              if (paramMatches) {
                for (const paramMatch of paramMatches) {
                  const paramKeyMatch = paramMatch.match(/<(\w+)>(.*?)<\/\w+>/);
                  if (paramKeyMatch) {
                    parameters[paramKeyMatch[1]] = paramKeyMatch[2].trim();
                  }
                }
              }

              toolCalls.push({
                name: toolName,
                parameters
              });
            }
          }
        }
        return toolCalls;
      }

      return [];
    } catch (error) {
      console.warn(chalk.yellow('⚠️  解析工具调用响应失败'));
      return [];
    }
  }



  /**
   * 推断修复方法（从错误信息）
   */
  inferApproachFromError(error) {
    if (error.includes('XML') || error.includes('解析')) {
      return '响应格式修复';
    }
    if (error.includes('导入') || error.includes('import')) {
      return '导入语句修复';
    }
    if (error.includes('语法') || error.includes('syntax')) {
      return '语法错误修复';
    }
    if (error.includes('类型') || error.includes('type')) {
      return 'TypeScript类型修复';
    }
    return '通用修复';
  }

  /**
   * 记录失败的尝试
   */
  recordFailedAttempt(filePath, attemptNumber, error) {
    const attemptKey = `attempt_${attemptNumber}`;
    
    if (!this.attemptHistory[attemptKey]) {
      this.attemptHistory[attemptKey] = [];
    }

    this.attemptHistory[attemptKey].push({
      filePath,
      error,
      timestamp: new Date().toISOString(),
      approach: this.inferApproachFromError(error)
    });
  }
}

module.exports = BuildFixAgent;
