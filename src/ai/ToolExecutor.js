const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { spawn } = require('child_process');

/**
 * ToolExecutor - 工具执行器
 * 
 * 专门负责：
 * - 定义可用工具
 * - 执行工具调用（文件读写、目录列表、命令执行）
 * - 工具调用结果管理
 * - 工具调用的错误处理
 */
class ToolExecutor {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      dryRun: false,
      verbose: false,
      commandTimeout: 30000, // 30秒超时
      ...options
    };

    // 允许执行的命令白名单 - 安全考虑
    this.allowedCommands = [
      // 包管理器
      'npm', 'yarn', 'pnpm', 'cnpm',
      // Node.js 相关
      'node', 'npx', 'nvm',
      // 版本控制
      'git',
      // 构建工具
      'webpack', 'vite', 'rollup', 'babel',
      // 任务运行器
      'gulp', 'grunt',
      // 测试工具
      'jest', 'mocha', 'vitest',
      // 代码检查和格式化
      'eslint', 'prettier', 'tslint',
      // TypeScript
      'tsc', 'typescript',
      // 基础系统命令
      'ls', 'dir', 'cat', 'type', 'head', 'tail',
      'mkdir', 'rmdir', 'find', 'grep',
      'cp', 'mv', 'rm', 'chmod',
      'echo', 'pwd', 'which', 'where',
      // Vue CLI
      'vue'
    ];

    // 工具定义 - 类似 Augment 的工具系统
    this.tools = [
      {
        name: 'read_file',
        description: '读取项目中的文件内容',
        parameters: {
          type: 'object',
          properties: {
            file_path: {
              type: 'string',
              description: '相对于项目根目录的文件路径'
            }
          },
          required: ['file_path']
        }
      },
      {
        name: 'write_file',
        description: '写入或修改项目中的文件',
        parameters: {
          type: 'object',
          properties: {
            file_path: {
              type: 'string',
              description: '相对于项目根目录的文件路径'
            },
            content: {
              type: 'string',
              description: '要写入的文件内容'
            }
          },
          required: ['file_path', 'content']
        }
      },
      {
        name: 'list_files',
        description: '列出项目目录中的文件',
        parameters: {
          type: 'object',
          properties: {
            directory: {
              type: 'string',
              description: '要列出的目录路径，相对于项目根目录'
            },
            pattern: {
              type: 'string',
              description: '文件匹配模式，如 \'*.vue\' 或 \'*.js\''
            }
          },
          required: ['directory']
        }
      },
      {
        name: 'run_command',
        description: '在项目目录中执行命令行命令（如 npm install, git status 等）',
        parameters: {
          type: 'object',
          properties: {
            command: {
              type: 'string',
              description: '要执行的命令，如 "npm install" 或 "git status"'
            },
            args: {
              type: 'array',
              items: { type: 'string' },
              description: '命令参数数组（可选，如果未提供则从command中解析）'
            },
            working_directory: {
              type: 'string',
              description: '执行命令的工作目录，相对于项目根目录（可选，默认为项目根目录）'
            }
          },
          required: ['command']
        }
      }
    ];
  }

  /**
   * 获取工具定义
   */
  getTools() {
    return this.tools;
  }

  /**
   * 获取工具描述（用于AI提示词）
   */
  getToolsDescription() {
    return this.tools.map(tool => `- ${tool.name}: ${tool.description}`).join('\n');
  }

  /**
   * 获取允许的命令列表
   */
  getAllowedCommands() {
    return [...this.allowedCommands];
  }

  /**
   * 添加允许的命令
   */
  addAllowedCommand(command) {
    if (typeof command === 'string' && !this.allowedCommands.includes(command)) {
      this.allowedCommands.push(command);
      return true;
    }
    return false;
  }

  /**
   * 添加多个允许的命令
   */
  addAllowedCommands(commands) {
    if (!Array.isArray(commands)) {
      return false;
    }
    
    let added = 0;
    for (const command of commands) {
      if (this.addAllowedCommand(command)) {
        added++;
      }
    }
    return added;
  }

  /**
   * 移除允许的命令
   */
  removeAllowedCommand(command) {
    const index = this.allowedCommands.indexOf(command);
    if (index > -1) {
      this.allowedCommands.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * 执行工具调用
   */
  async executeToolCall(toolName, parameters) {
    try {
      if (this.options.verbose) {
        console.log(chalk.gray(`    🔧 执行工具: ${toolName}(${JSON.stringify(parameters)})`));
      }

      switch (toolName) {
        case 'read_file':
          return await this.readFile(parameters.file_path);
        case 'write_file':
          return await this.writeFile(parameters.file_path, parameters.content);
        case 'list_files':
          return await this.listFiles(parameters.directory, parameters.pattern);
        case 'run_command':
          return await this.runCommand(parameters.command, parameters.args, parameters.working_directory);
        default:
          return {
            success: false,
            error: `未知的工具: ${toolName}`
          };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 批量执行工具调用
   */
  async executeToolCalls(toolCalls) {
    const results = new Map();

    if (this.options.verbose) {
      console.log(chalk.gray(`    🔧 批量执行 ${toolCalls.length} 个工具调用...`));
    }

    for (const toolCall of toolCalls) {
      try {
        const result = await this.executeToolCall(toolCall.name, toolCall.parameters);
        const resultKey = this.generateResultKey(toolCall);

        if (result.success) {
          results.set(resultKey, {
            toolCall,
            result,
            success: true
          });

          if (this.options.verbose) {
            console.log(chalk.green(`      ✅ ${toolCall.name}: 成功`));
          }
        } else {
          results.set(resultKey, {
            toolCall,
            result,
            success: false
          });

          if (this.options.verbose) {
            console.log(chalk.yellow(`      ⚠️  ${toolCall.name}: ${result.error}`));
          }
        }
      } catch (error) {
        const resultKey = this.generateResultKey(toolCall);
        results.set(resultKey, {
          toolCall,
          result: { success: false, error: error.message },
          success: false
        });

        if (this.options.verbose) {
          console.log(chalk.red(`      ❌ ${toolCall.name}: 异常 - ${error.message}`));
        }
      }
    }

    if (this.options.verbose) {
      const successCount = Array.from(results.values()).filter(r => r.success).length;
      console.log(chalk.gray(`    📊 工具调用完成: ${successCount}/${toolCalls.length} 成功`));
    }

    return results;
  }

  /**
   * 生成结果键名
   */
  generateResultKey(toolCall) {
    switch (toolCall.name) {
      case 'read_file':
        return toolCall.parameters.file_path;
      case 'list_files':
        return `list_${toolCall.parameters.directory}`;
      case 'write_file':
        return `write_${toolCall.parameters.file_path}`;
      case 'run_command':
        return `run_${toolCall.parameters.command}_${Date.now()}`;
      default:
        return `${toolCall.name}_${Date.now()}`;
    }
  }

  /**
   * 读取文件工具
   */
  async readFile(filePath) {
    try {
      const fullPath = path.join(this.projectPath, filePath);

      if (!await fs.pathExists(fullPath)) {
        return {
          success: false,
          error: '文件不存在'
        };
      }

      const content = await fs.readFile(fullPath, 'utf8');
      return {
        success: true,
        content,
        filePath: filePath,
        size: content.length
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 写入文件工具
   */
  async writeFile(filePath, content) {
    try {
      if (this.options.dryRun) {
        if (this.options.verbose) {
          console.log(chalk.gray(`      [预览模式] 将写入文件: ${filePath} (${content.length} 字符)`));
        }
        return {
          success: true,
          message: '预览模式，未实际写入',
          dryRun: true,
          filePath: filePath,
          contentLength: content.length
        };
      }

      const fullPath = path.join(this.projectPath, filePath);

      // 确保目录存在
      await fs.ensureDir(path.dirname(fullPath));

      // 备份原文件
      if (await fs.pathExists(fullPath)) {
        const backupPath = `${fullPath}.backup.${Date.now()}`;
        await fs.copy(fullPath, backupPath);
        
        if (this.options.verbose) {
          console.log(chalk.gray(`      💾 已备份原文件: ${path.basename(backupPath)}`));
        }
      }

      await fs.writeFile(fullPath, content, 'utf8');
      
      return {
        success: true,
        message: '文件写入成功',
        filePath: filePath,
        contentLength: content.length
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 列出文件工具
   */
  async listFiles(directory, pattern) {
    try {
      const fullPath = path.join(this.projectPath, directory);

      if (!await fs.pathExists(fullPath)) {
        return {
          success: false,
          error: '目录不存在'
        };
      }

      const stats = await fs.stat(fullPath);
      if (!stats.isDirectory()) {
        return {
          success: false,
          error: '指定路径不是目录'
        };
      }

      const files = await fs.readdir(fullPath);
      let filteredFiles = files;

      if (pattern) {
        const glob = require('glob');
        filteredFiles = files.filter(file => glob.minimatch(file, pattern));
      }

      // 获取文件详细信息
      const filesWithInfo = [];
      for (const file of filteredFiles) {
        try {
          const fileFullPath = path.join(fullPath, file);
          const fileStat = await fs.stat(fileFullPath);
          filesWithInfo.push({
            name: file,
            isDirectory: fileStat.isDirectory(),
            size: fileStat.size,
            modified: fileStat.mtime
          });
        } catch (error) {
          filesWithInfo.push({
            name: file,
            isDirectory: false,
            size: 0,
            modified: null,
            error: error.message
          });
        }
      }

      return {
        success: true,
        files: filteredFiles,
        filesWithInfo: filesWithInfo,
        directory: directory,
        totalCount: filteredFiles.length
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 验证文件路径
   */
  validateFilePath(filePath) {
    if (!filePath || typeof filePath !== 'string') {
      return { valid: false, error: '文件路径不能为空' };
    }

    // 防止路径遍历攻击
    if (filePath.includes('..')) {
      return { valid: false, error: '文件路径不能包含 ..' };
    }

    // 检查是否是绝对路径
    if (path.isAbsolute(filePath)) {
      return { valid: false, error: '文件路径必须是相对路径' };
    }

    // 检查文件扩展名
    const allowedExtensions = ['.vue', '.js', '.ts', '.jsx', '.tsx', '.json', '.scss', '.css', '.html'];
    const ext = path.extname(filePath);
    
    if (ext && !allowedExtensions.includes(ext)) {
      return { valid: false, error: `不支持的文件类型: ${ext}` };
    }

    return { valid: true };
  }

  /**
   * 获取上下文文件信息（用于AI）
   */
  formatContextFiles(results) {
    let contextSection = '';
    
    if (results.size === 0) {
      return contextSection;
    }

    contextSection = '\n**相关文件上下文**：\n';

    for (const [key, resultInfo] of results) {
      if (!resultInfo.success) {
        continue;
      }

      const { toolCall, result } = resultInfo;

      if (toolCall.name === 'read_file' && result.content) {
        const truncatedContent = result.content.length > 1000
          ? result.content.substring(0, 1000) + '\n... (内容已截断)'
          : result.content;

        contextSection += `\n**文件**: ${result.filePath} (${result.size} 字符)\n\`\`\`\n${truncatedContent}\n\`\`\`\n`;
      } else if (toolCall.name === 'list_files' && result.files) {
        contextSection += `\n**目录**: ${result.directory} (${result.totalCount} 个文件)\n`;
        contextSection += `文件列表: ${result.files.join(', ')}\n`;
        
        if (result.filesWithInfo) {
          const directories = result.filesWithInfo.filter(f => f.isDirectory).length;
          const files = result.filesWithInfo.filter(f => !f.isDirectory).length;
          contextSection += `文件分布: ${files} 个文件, ${directories} 个目录\n`;
        }
      } else if (toolCall.name === 'run_command') {
        const cmdDisplay = result.args 
          ? `${result.command} ${result.args.join(' ')}`
          : toolCall.parameters.command;
          
        contextSection += `\n**命令执行**: ${cmdDisplay}\n`;
        
        if (result.dryRun) {
          contextSection += `状态: 预览模式\n`;
          contextSection += `工作目录: ${result.workingDirectory}\n`;
        } else {
          contextSection += `状态: ${resultInfo.success ? '成功' : '失败'}\n`;
          contextSection += `退出码: ${result.exitCode || 'N/A'}\n`;
          contextSection += `工作目录: ${result.workingDirectory}\n`;
          
          if (result.stdout) {
            const truncatedOutput = result.stdout.length > 500
              ? result.stdout.substring(0, 500) + '\n... (输出已截断)'
              : result.stdout;
            contextSection += `\n**标准输出**:\n\`\`\`\n${truncatedOutput}\n\`\`\`\n`;
          }
          
          if (result.stderr) {
            const truncatedError = result.stderr.length > 300
              ? result.stderr.substring(0, 300) + '\n... (错误已截断)'
              : result.stderr;
            contextSection += `\n**错误输出**:\n\`\`\`\n${truncatedError}\n\`\`\`\n`;
          }
        }
      }
    }

    return contextSection;
  }

  /**
   * 获取工具执行统计
   */
  getExecutionStats(results) {
    const stats = {
      total: results.size,
      success: 0,
      failed: 0,
      byTool: {}
    };

    for (const [key, resultInfo] of results) {
      const toolName = resultInfo.toolCall.name;
      
      if (!stats.byTool[toolName]) {
        stats.byTool[toolName] = { total: 0, success: 0, failed: 0 };
      }
      
      stats.byTool[toolName].total++;
      
      if (resultInfo.success) {
        stats.success++;
        stats.byTool[toolName].success++;
      } else {
        stats.failed++;
        stats.byTool[toolName].failed++;
      }
    }

    return stats;
  }

  /**
   * 执行命令工具
   */
  async runCommand(command, args, workingDirectory = '') {
    try {
      // 解析命令和参数
      let cmdName, cmdArgs;
      
      if (args && Array.isArray(args)) {
        // 如果提供了 args 数组，直接使用
        cmdName = command;
        cmdArgs = args;
      } else {
        // 从命令字符串中解析
        const parts = command.trim().split(/\s+/);
        cmdName = parts[0];
        cmdArgs = parts.slice(1);
      }

      // 验证命令安全性
      const validation = this.validateCommand(cmdName, cmdArgs);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error
        };
      }

      // Dry run 模式
      if (this.options.dryRun) {
        if (this.options.verbose) {
          console.log(chalk.gray(`      [预览模式] 将执行命令: ${cmdName} ${cmdArgs.join(' ')}`));
          console.log(chalk.gray(`      [预览模式] 工作目录: ${workingDirectory || this.projectPath}`));
        }
        return {
          success: true,
          message: '预览模式，未实际执行命令',
          dryRun: true,
          command: cmdName,
          args: cmdArgs,
          workingDirectory: workingDirectory || this.projectPath
        };
      }

      const workDir = workingDirectory 
        ? path.resolve(this.projectPath, workingDirectory)
        : this.projectPath;

      // 确保工作目录存在且安全
      if (!await fs.pathExists(workDir)) {
        return {
          success: false,
          error: `工作目录不存在: ${workingDirectory}`
        };
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`      🚀 执行命令: ${cmdName} ${cmdArgs.join(' ')}`));
        console.log(chalk.gray(`      📁 工作目录: ${workDir}`));
      }

      const options = {
        cwd: workDir,
        timeout: this.options.commandTimeout,
        maxBuffer: 1024 * 1024, // 1MB
        stdio: ['pipe', 'pipe', 'pipe']
      };

      const result = await new Promise((resolve, reject) => {
        const childProcess = spawn(cmdName, cmdArgs, options);

        let stdout = '';
        let stderr = '';

        childProcess.stdout.on('data', data => {
          const chunk = data.toString();
          stdout += chunk;
          
          if (this.options.verbose) {
            // 实时输出标准输出（但不包含换行符混乱）
            process.stdout.write(chalk.gray('      │ ') + chunk);
          }
        });

        childProcess.stderr.on('data', data => {
          const chunk = data.toString();
          stderr += chunk;
          
          if (this.options.verbose) {
            // 实时输出错误输出
            process.stderr.write(chalk.gray('      │ ') + chalk.yellow(chunk));
          }
        });

        childProcess.on('close', code => {
          resolve({
            success: code === 0,
            code,
            stdout: stdout.trim(),
            stderr: stderr.trim()
          });
        });

        childProcess.on('error', err => {
          reject(new Error(`命令执行异常: ${err.message}`));
        });

        // 处理超时
        setTimeout(() => {
          if (!childProcess.killed) {
            childProcess.kill('SIGTERM');
            reject(new Error(`命令执行超时 (${this.options.commandTimeout}ms)`));
          }
        }, this.options.commandTimeout);
      });

      if (result.success) {
        if (this.options.verbose) {
          console.log(chalk.green(`      ✅ 命令执行成功 (退出码: ${result.code})`));
        }
        
        return {
          success: true,
          command: cmdName,
          args: cmdArgs,
          workingDirectory: workDir,
          exitCode: result.code,
          stdout: result.stdout,
          stderr: result.stderr,
          output: result.stdout // 向后兼容
        };
      } else {
        if (this.options.verbose) {
          console.log(chalk.yellow(`      ⚠️  命令执行失败 (退出码: ${result.code})`));
        }
        
        return {
          success: false,
          command: cmdName,
          args: cmdArgs,
          workingDirectory: workDir,
          exitCode: result.code,
          stdout: result.stdout,
          stderr: result.stderr,
          error: `命令执行失败 (退出码: ${result.code})\n${result.stderr || '无错误信息'}`
        };
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.red(`      ❌ 命令执行异常: ${error.message}`));
      }
      
      return {
        success: false,
        error: error.message,
        command: command,
        args: args
      };
    }
  }

  /**
   * 验证命令安全性
   */
  validateCommand(cmdName, cmdArgs = []) {
    if (!cmdName || typeof cmdName !== 'string') {
      return { valid: false, error: '命令名不能为空' };
    }

    // 检查命令是否在白名单中
    if (!this.allowedCommands.includes(cmdName)) {
      return { 
        valid: false, 
        error: `不允许执行的命令: ${cmdName}。允许的命令: ${this.allowedCommands.join(', ')}` 
      };
    }

    // 特殊命令的额外验证
    if (cmdName === 'rm' && cmdArgs.includes('-rf')) {
      return { 
        valid: false, 
        error: '为安全考虑，不允许执行 rm -rf 命令' 
      };
    }

    if (cmdName === 'git' && cmdArgs.includes('reset')) {
      if (cmdArgs.includes('--hard')) {
        return { 
          valid: false, 
          error: '为安全考虑，不允许执行 git reset --hard 命令' 
        };
      }
    }

    // 检查危险参数
    const dangerousArgs = ['&&', '||', ';', '|', '>', '<', '`', '$'];
    for (const arg of cmdArgs) {
      for (const dangerous of dangerousArgs) {
        if (arg.includes(dangerous)) {
          return { 
            valid: false, 
            error: `参数中包含危险字符: ${dangerous}` 
          };
        }
      }
    }

    return { valid: true };
  }
}

module.exports = ToolExecutor; 