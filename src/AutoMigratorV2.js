const chalk = require('chalk');
const path = require('path');

// 导入新的阶段化架构
const MigrationOrchestrator = require('./migration/MigrationOrchestrator');
const ProjectAnalysisPhase = require('./migration/phases/ProjectAnalysisPhase');
const DependencyUpgradePhase = require('./migration/phases/DependencyUpgradePhase');
const CodeMigrationPhase = require('./migration/phases/CodeMigrationPhase');
const IntelligentRepairPhase = require('./migration/phases/IntelligentRepairPhase');
const ValidationPhase = require('./migration/phases/ValidationPhase');

/**
 * Vue 2 到 Vue 3 自动迁移工具 V2
 * 基于阶段化架构的新版本
 */
class AutoMigratorV2 {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = this.normalizeOptions(options);
    
    // 初始化协调器
    this.orchestrator = new MigrationOrchestrator(this.projectPath, this.options);
    
    // 注册所有迁移阶段
    this.registerMigrationPhases();
  }

  /**
   * 标准化选项
   */
  normalizeOptions(options) {
    return {
      // 基础选项
      sourceToTargetMode: options.sourceToTargetMode || false,
      sourceProjectPath: options.sourceProjectPath,
      targetProjectPath: options.targetProjectPath,
      
      // 跳过选项
      skipDependencyCheck: options.skipDependencyCheck || false,
      skipAIRepair: options.skipAIRepair || false,
      skipESLint: options.skipESLint || false,
      skipBuild: options.skipBuild || false,
      
      // AI 配置
      aiApiKey: options.aiApiKey || this.detectAIApiKey(),
      
      // 构建配置
      buildCommand: options.buildCommand || 'npm run build',
      testCommand: options.testCommand || 'npm test',
      
      // 修复配置
      maxRepairAttempts: options.maxRepairAttempts || 3,
      
      // 输出配置
      dryRun: options.dryRun || false,
      verbose: options.verbose || false,
      
      // 向后兼容
      newDirectoryMode: options.newDirectoryMode || false,
      destinationPath: options.destinationPath
    };
  }

  /**
   * 注册迁移阶段
   */
  registerMigrationPhases() {
    const phases = [
      // 阶段 1: 项目分析与准备
      new ProjectAnalysisPhase(this.projectPath, {
        sourceToTargetMode: this.options.sourceToTargetMode,
        sourceProjectPath: this.options.sourceProjectPath,
        targetProjectPath: this.options.targetProjectPath,
        aiApiKey: this.options.aiApiKey,
        verbose: this.options.verbose,
        dryRun: this.options.dryRun
      }),

      // 阶段 2: 依赖升级与映射
      new DependencyUpgradePhase(this.projectPath, {
        sourceToTargetMode: this.options.sourceToTargetMode,
        targetProjectPath: this.options.targetProjectPath,
        verbose: this.options.verbose,
        dryRun: this.options.dryRun
      }),

      // 阶段 3: 代码迁移与转换
      new CodeMigrationPhase(this.projectPath, {
        sourceToTargetMode: this.options.sourceToTargetMode,
        targetProjectPath: this.options.targetProjectPath,
        aiApiKey: this.options.aiApiKey,
        verbose: this.options.verbose,
        dryRun: this.options.dryRun
      }),

      // 阶段 4: 智能修复与优化
      new IntelligentRepairPhase(this.projectPath, {
        sourceToTargetMode: this.options.sourceToTargetMode,
        targetProjectPath: this.options.targetProjectPath,
        aiApiKey: this.options.aiApiKey,
        buildCommand: this.options.buildCommand,
        maxRepairAttempts: this.options.maxRepairAttempts,
        skipAIRepair: this.options.skipAIRepair,
        skipESLint: this.options.skipESLint,
        skipBuild: this.options.skipBuild,
        verbose: this.options.verbose,
        dryRun: this.options.dryRun
      }),

      // 阶段 5: 验证与完善
      new ValidationPhase(this.projectPath, {
        sourceToTargetMode: this.options.sourceToTargetMode,
        targetProjectPath: this.options.targetProjectPath,
        buildCommand: this.options.buildCommand,
        testCommand: this.options.testCommand,
        verbose: this.options.verbose,
        dryRun: this.options.dryRun
      })
    ];

    // 根据选项过滤阶段
    const filteredPhases = this.filterPhases(phases);
    
    this.orchestrator.registerPhases(filteredPhases);
  }

  /**
   * 根据选项过滤阶段
   */
  filterPhases(phases) {
    return phases.filter(phase => {
      // 根据跳过选项过滤阶段
      if (this.options.skipDependencyCheck && phase.name === '依赖升级与映射') {
        return false;
      }
      
      if (this.options.skipBuild && phase.name === '智能修复与优化') {
        return false;
      }
      
      return true;
    });
  }

  /**
   * 执行迁移
   */
  async migrate() {
    try {
      console.log(chalk.bold.blue('\n🚀 Vue 2 到 Vue 3 迁移工具 V2\n'));
      
      if (this.options.sourceToTargetMode) {
        console.log(chalk.gray(`源项目: ${this.options.sourceProjectPath}`));
        console.log(chalk.gray(`目标项目: ${this.options.targetProjectPath}`));
      } else {
        console.log(chalk.gray(`项目路径: ${this.projectPath}`));
      }
      
      console.log(chalk.gray(`开始时间: ${new Date().toLocaleString()}\n`));

      // 执行迁移
      const result = await this.orchestrator.execute();
      
      // 打印最终结果
      this.printFinalResult(result);
      
      return result;

    } catch (error) {
      console.error(chalk.red('\n❌ 迁移失败:'), error.message);
      if (this.options.verbose) {
        console.error(error.stack);
      }
      throw error;
    }
  }

  /**
   * 打印最终结果
   */
  printFinalResult(result) {
    if (result.success) {
      console.log(chalk.bold.green('\n🎉 Vue 2 到 Vue 3 迁移完成！'));
      
      if (this.options.sourceToTargetMode) {
        console.log(chalk.green(`✅ 迁移后的项目位于: ${this.options.targetProjectPath}`));
      }
      
      // 显示后续建议
      this.printRecommendations(result);
      
    } else {
      console.log(chalk.bold.red('\n❌ 迁移未完全成功'));
      console.log(chalk.yellow('请查看详细日志并手动修复剩余问题'));
    }
  }

  /**
   * 打印建议
   */
  printRecommendations(result) {
    const context = result.context;
    const validationResult = context['验证与完善'];
    
    if (validationResult?.recommendations?.length > 0) {
      console.log('\n' + chalk.bold('💡 后续建议:'));
      validationResult.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    }
  }

  /**
   * 检测 AI API Key
   */
  detectAIApiKey() {
    const envKeys = {
      deepseek: 'DEEPSEEK_API_KEY',
      glm: 'GLM_API_KEY',
      openai: 'OPENAI_API_KEY'
    };

    // 按优先级顺序检查
    const providers = ['deepseek', 'glm', 'openai'];
    for (const provider of providers) {
      const key = process.env[envKeys[provider]];
      if (key) return key;
    }

    return null;
  }

  /**
   * 获取迁移统计
   */
  getStats() {
    return this.orchestrator.getStats();
  }

  /**
   * 获取执行上下文
   */
  getContext() {
    return this.orchestrator.getContext();
  }

  /**
   * 向后兼容：支持旧的 API
   */
  async executeMigration() {
    console.log(chalk.yellow('⚠️  executeMigration() 已废弃，请使用 migrate()'));
    return this.migrate();
  }

  /**
   * 向后兼容：获取工作路径
   */
  get workingPath() {
    return this.options.sourceToTargetMode ? 
      this.options.targetProjectPath : 
      this.projectPath;
  }
}

module.exports = AutoMigratorV2;
