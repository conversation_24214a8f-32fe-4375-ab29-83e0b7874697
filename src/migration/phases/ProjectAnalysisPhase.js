const MigrationPhase = require('../MigrationPhase');
const ProjectDetector = require('../../utils/projectDetector');
const MigrationStrategySelector = require('../../third-party/migrationStrategySelector');
const DependencyChecker = require('../../dependency/dependencyChecker');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * 项目分析与准备阶段
 * 负责分析项目结构、选择迁移策略、准备文件复制
 */
class ProjectAnalysisPhase extends MigrationPhase {
  constructor(projectPath, options = {}) {
    super('项目分析与准备', projectPath, options);
    this.sourceProjectPath = options.sourceProjectPath;
    this.targetProjectPath = options.targetProjectPath;
    this.sourceToTargetMode = options.sourceToTargetMode || false;
  }

  /**
   * 执行项目分析阶段
   */
  async execute(context) {
    const result = {
      success: true,
      projectStructure: null,
      compatibilityReport: null,
      migrationStrategy: null,
      filesCopied: false
    };

    try {
      // 1. AI 项目结构分析
      console.log(chalk.blue('🔍 分析项目结构...'));
      result.projectStructure = await this.analyzeProjectStructure();

      // 2. 依赖兼容性预分析
      console.log(chalk.blue('📦 分析依赖兼容性...'));
      result.compatibilityReport = await this.analyzeDependencyCompatibility();

      // 3. AI 迁移策略选择
      console.log(chalk.blue('🎯 选择迁移策略...'));
      result.migrationStrategy = await this.selectMigrationStrategy(result.projectStructure);

      // 4. 智能文件复制准备
      if (this.sourceToTargetMode) {
        console.log(chalk.blue('📁 准备文件复制...'));
        await this.smartFileCopy(result.migrationStrategy);
        result.filesCopied = true;
      }

      return result;

    } catch (error) {
      result.success = false;
      result.error = error.message;
      throw error;
    }
  }

  /**
   * AI 项目结构分析
   */
  async analyzeProjectStructure() {
    const detector = new ProjectDetector(this.sourceProjectPath || this.projectPath);
    
    // 基础项目检测
    const basicInfo = await detector.detectProject();
    
    // 扩展分析
    const structure = {
      ...basicInfo,
      complexity: await this.assessProjectComplexity(detector),
      riskFactors: await this.identifyRiskFactors(detector),
      estimatedEffort: await this.estimateMigrationEffort(detector)
    };

    if (this.options.verbose) {
      console.log(chalk.gray('  项目结构分析结果:'));
      console.log(chalk.gray(`    框架: ${structure.framework}`));
      console.log(chalk.gray(`    复杂度: ${structure.complexity}`));
      console.log(chalk.gray(`    风险因素: ${structure.riskFactors.length} 个`));
    }

    return structure;
  }

  /**
   * 评估项目复杂度
   */
  async assessProjectComplexity(detector) {
    const factors = {
      fileCount: 0,
      componentCount: 0,
      dependencyCount: 0,
      customDirectives: 0,
      mixins: 0,
      plugins: 0
    };

    try {
      // 统计文件数量
      const srcPath = path.join(detector.projectPath, 'src');
      if (await fs.pathExists(srcPath)) {
        factors.fileCount = await this.countFiles(srcPath, ['.vue', '.js', '.ts']);
        factors.componentCount = await this.countFiles(srcPath, ['.vue']);
      }

      // 统计依赖数量
      const packageJsonPath = path.join(detector.projectPath, 'package.json');
      if (await fs.pathExists(packageJsonPath)) {
        const packageJson = await fs.readJson(packageJsonPath);
        factors.dependencyCount = Object.keys({
          ...packageJson.dependencies,
          ...packageJson.devDependencies
        }).length;
      }

      // 基于因素计算复杂度
      let complexity = 'low';
      if (factors.fileCount > 100 || factors.dependencyCount > 50) {
        complexity = 'high';
      } else if (factors.fileCount > 50 || factors.dependencyCount > 25) {
        complexity = 'medium';
      }

      return complexity;

    } catch (error) {
      console.log(chalk.yellow('⚠️  复杂度评估失败，使用默认值'));
      return 'medium';
    }
  }

  /**
   * 识别风险因素
   */
  async identifyRiskFactors(detector) {
    const risks = [];

    try {
      // 检查是否使用了已知的问题依赖
      const problematicDeps = [
        'vue-property-decorator',
        'vue-class-component',
        'vuex-class',
        'vue-router@3'
      ];

      const packageJsonPath = path.join(detector.projectPath, 'package.json');
      if (await fs.pathExists(packageJsonPath)) {
        const packageJson = await fs.readJson(packageJsonPath);
        const allDeps = {
          ...packageJson.dependencies,
          ...packageJson.devDependencies
        };

        problematicDeps.forEach(dep => {
          const depName = dep.split('@')[0];
          if (allDeps[depName]) {
            risks.push({
              type: 'dependency',
              name: depName,
              description: `${depName} 需要特殊处理`
            });
          }
        });
      }

      // 检查自定义指令
      const srcPath = path.join(detector.projectPath, 'src');
      if (await fs.pathExists(srcPath)) {
        const hasCustomDirectives = await this.searchInFiles(srcPath, /Vue\.directive|app\.directive/);
        if (hasCustomDirectives) {
          risks.push({
            type: 'code',
            name: 'custom-directives',
            description: '发现自定义指令，需要手动迁移'
          });
        }
      }

    } catch (error) {
      console.log(chalk.yellow('⚠️  风险因素识别失败'));
    }

    return risks;
  }

  /**
   * 估算迁移工作量
   */
  async estimateMigrationEffort(detector) {
    // 基于项目复杂度和风险因素估算
    const complexity = await this.assessProjectComplexity(detector);
    const risks = await this.identifyRiskFactors(detector);

    let effort = 'low';
    if (complexity === 'high' || risks.length > 5) {
      effort = 'high';
    } else if (complexity === 'medium' || risks.length > 2) {
      effort = 'medium';
    }

    return effort;
  }

  /**
   * 分析依赖兼容性
   */
  async analyzeDependencyCompatibility() {
    try {
      const checker = new DependencyChecker(this.sourceProjectPath || this.projectPath);
      return await checker.checkCompatibility();
    } catch (error) {
      console.log(chalk.yellow('⚠️  依赖兼容性分析失败'));
      return {
        compatible: [],
        incompatible: [],
        unknown: []
      };
    }
  }

  /**
   * 选择迁移策略
   */
  async selectMigrationStrategy(projectStructure) {
    try {
      const strategySelector = new MigrationStrategySelector(
        this.sourceProjectPath || this.projectPath,
        {
          aiApiKey: this.options.aiApiKey,
          verbose: this.options.verbose
        }
      );

      const strategyResult = await strategySelector.selectStrategy();
      return strategyResult.strategy;

    } catch (error) {
      console.log(chalk.yellow('⚠️  策略选择失败，使用默认策略'));
      return {
        name: 'default',
        description: '默认迁移策略',
        steps: ['dependency-upgrade', 'code-migration', 'build-fix']
      };
    }
  }

  /**
   * 智能文件复制
   */
  async smartFileCopy(strategy) {
    if (!this.sourceToTargetMode) return;

    try {
      // 确保目标目录存在
      await fs.ensureDir(this.targetProjectPath);

      // 复制 src 目录
      const sourceSrcPath = path.join(this.sourceProjectPath, 'src');
      if (await fs.pathExists(sourceSrcPath)) {
        const targetSrcPath = path.join(this.targetProjectPath, 'src');
        console.log(chalk.gray(`   复制 src 目录...`));
        
        await fs.copy(sourceSrcPath, targetSrcPath, {
          overwrite: true,
          filter: this.getFileFilter()
        });
      }

      // 智能复制其他必要文件
      await this.copyAdditionalFiles(strategy);

    } catch (error) {
      throw new Error(`文件复制失败: ${error.message}`);
    }
  }

  /**
   * 获取文件过滤器
   */
  getFileFilter() {
    return (src) => {
      const fileName = path.basename(src);
      return !fileName.startsWith('.') &&
             fileName !== 'node_modules' &&
             !fileName.endsWith('.log') &&
             fileName !== 'dist' &&
             fileName !== 'build';
    };
  }

  /**
   * 复制额外必要文件
   */
  async copyAdditionalFiles(strategy) {
    const filesToCopy = [
      'public',
      'assets',
      'static',
      'types',
      'utils',
      'api',
      'store',
      'router',
      'components',
      'views',
      'layouts'
    ];

    for (const item of filesToCopy) {
      const sourcePath = path.join(this.sourceProjectPath, item);
      if (await fs.pathExists(sourcePath)) {
        const targetPath = path.join(this.targetProjectPath, item);
        console.log(chalk.gray(`   复制 ${item}...`));
        await fs.copy(sourcePath, targetPath, {
          overwrite: true,
          filter: this.getFileFilter()
        });
      }
    }
  }

  /**
   * 统计文件数量
   */
  async countFiles(dirPath, extensions) {
    let count = 0;
    try {
      const files = await fs.readdir(dirPath, { withFileTypes: true });
      for (const file of files) {
        const fullPath = path.join(dirPath, file.name);
        if (file.isDirectory()) {
          count += await this.countFiles(fullPath, extensions);
        } else if (extensions.some(ext => file.name.endsWith(ext))) {
          count++;
        }
      }
    } catch (error) {
      // 忽略无法访问的目录
    }
    return count;
  }

  /**
   * 在文件中搜索模式
   */
  async searchInFiles(dirPath, pattern) {
    try {
      const files = await fs.readdir(dirPath, { withFileTypes: true });
      for (const file of files) {
        const fullPath = path.join(dirPath, file.name);
        if (file.isDirectory()) {
          if (await this.searchInFiles(fullPath, pattern)) {
            return true;
          }
        } else if (file.name.endsWith('.js') || file.name.endsWith('.vue') || file.name.endsWith('.ts')) {
          const content = await fs.readFile(fullPath, 'utf8');
          if (pattern.test(content)) {
            return true;
          }
        }
      }
    } catch (error) {
      // 忽略错误
    }
    return false;
  }

  /**
   * 判断是否为关键错误
   */
  isCriticalError(error) {
    // 项目分析阶段的错误通常是关键的
    return true;
  }
}

module.exports = ProjectAnalysisPhase;
