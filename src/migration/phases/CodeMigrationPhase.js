const MigrationPhase = require('../MigrationPhase');
const CodeMigrator = require('../../vue/VueCodeMigrator');
const SassMigrator = require('../../sass/sassMigrator');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * 代码迁移与转换阶段
 * 支持并行处理不同类型文件的迁移
 */
class CodeMigrationPhase extends MigrationPhase {
  constructor(projectPath, options = {}) {
    super('代码迁移与转换', projectPath, options);
    this.sourceToTargetMode = options.sourceToTargetMode || false;
    this.targetProjectPath = options.targetProjectPath;
    this.workingPath = this.sourceToTargetMode ? this.targetProjectPath : this.projectPath;
    this.failedFiles = [];
  }

  /**
   * 获取阶段依赖
   */
  getDependencies() {
    return ['依赖升级与映射'];
  }

  /**
   * 执行代码迁移阶段
   */
  async execute(context) {
    const result = {
      success: true,
      vueComponentsMigrated: false,
      javascriptFilesMigrated: false,
      styleFilesMigrated: false,
      thirdPartyComponentsMigrated: false,
      totalFiles: 0,
      successFiles: 0,
      failedFiles: 0,
      failedFilesList: []
    };

    try {
      console.log(chalk.blue('🔄 开始并行代码迁移...'));

      // 并行处理不同类型的文件迁移
      const migrationTasks = await Promise.allSettled([
        this.migrateVueComponents(),
        this.migrateJavaScriptFiles(),
        this.migrateStyleFiles(),
        this.migrateThirdPartyComponents()
      ]);

      // 处理迁移结果
      const [vueResult, jsResult, styleResult, thirdPartyResult] = migrationTasks;

      // 汇总结果
      result.vueComponentsMigrated = vueResult.status === 'fulfilled' && vueResult.value.success;
      result.javascriptFilesMigrated = jsResult.status === 'fulfilled' && jsResult.value.success;
      result.styleFilesMigrated = styleResult.status === 'fulfilled' && styleResult.value.success;
      result.thirdPartyComponentsMigrated = thirdPartyResult.status === 'fulfilled' && thirdPartyResult.value.success;

      // 统计文件数量
      migrationTasks.forEach((task, index) => {
        if (task.status === 'fulfilled' && task.value) {
          result.totalFiles += task.value.totalFiles || 0;
          result.successFiles += task.value.successFiles || 0;
          result.failedFiles += task.value.failedFiles || 0;
          if (task.value.failedFilesList) {
            result.failedFilesList.push(...task.value.failedFilesList);
          }
        }
      });

      // 保存失败文件列表供后续阶段使用
      this.failedFiles = result.failedFilesList;

      // 记录失败的任务
      migrationTasks.forEach((task, index) => {
        if (task.status === 'rejected') {
          const taskNames = ['Vue组件', 'JavaScript文件', '样式文件', '第三方组件'];
          console.log(chalk.yellow(`⚠️  ${taskNames[index]}迁移失败: ${task.reason.message}`));
        }
      });

      if (this.options.verbose) {
        console.log(chalk.gray(`  迁移统计: 总计 ${result.totalFiles} 个文件`));
        console.log(chalk.gray(`  成功: ${result.successFiles}, 失败: ${result.failedFiles}`));
      }

      return result;

    } catch (error) {
      result.success = false;
      result.error = error.message;
      throw error;
    }
  }

  /**
   * 迁移 Vue 组件
   */
  async migrateVueComponents() {
    console.log(chalk.blue('  🔧 迁移 Vue 组件...'));
    
    try {
      const migrator = new CodeMigrator(this.workingPath, {
        enableMigrationStrategy: false, // 使用已有的策略分析结果
        aiApiKey: this.options.aiApiKey,
        verbose: this.options.verbose,
        dryRun: this.options.dryRun,
        fileTypes: ['.vue'] // 只处理 Vue 文件
      });

      const result = await migrator.migrate();
      
      return {
        success: true,
        totalFiles: result.total || 0,
        successFiles: result.success || 0,
        failedFiles: result.failed || 0,
        failedFilesList: result.failedFiles || []
      };

    } catch (error) {
      console.log(chalk.yellow('⚠️  Vue 组件迁移失败'));
      return {
        success: false,
        error: error.message,
        totalFiles: 0,
        successFiles: 0,
        failedFiles: 0,
        failedFilesList: []
      };
    }
  }

  /**
   * 迁移 JavaScript 文件
   */
  async migrateJavaScriptFiles() {
    console.log(chalk.blue('  📜 迁移 JavaScript 文件...'));
    
    try {
      const migrator = new CodeMigrator(this.workingPath, {
        enableMigrationStrategy: false,
        aiApiKey: this.options.aiApiKey,
        verbose: this.options.verbose,
        dryRun: this.options.dryRun,
        fileTypes: ['.js', '.ts'] // 只处理 JS/TS 文件
      });

      const result = await migrator.migrate();
      
      return {
        success: true,
        totalFiles: result.total || 0,
        successFiles: result.success || 0,
        failedFiles: result.failed || 0,
        failedFilesList: result.failedFiles || []
      };

    } catch (error) {
      console.log(chalk.yellow('⚠️  JavaScript 文件迁移失败'));
      return {
        success: false,
        error: error.message,
        totalFiles: 0,
        successFiles: 0,
        failedFiles: 0,
        failedFilesList: []
      };
    }
  }

  /**
   * 迁移样式文件
   */
  async migrateStyleFiles() {
    console.log(chalk.blue('  🎨 迁移样式文件...'));
    
    try {
      // 只在源到目标模式下执行 Sass 迁移
      if (this.sourceToTargetMode) {
        const sassMigrator = new SassMigrator(this.targetProjectPath, {
          verbose: this.options.verbose,
          dryRun: this.options.dryRun
        });

        const result = await sassMigrator.migrate();
        
        return {
          success: true,
          totalFiles: result.processedFiles || 0,
          successFiles: (result.processedFiles || 0) - (result.errorFiles || 0),
          failedFiles: result.errorFiles || 0,
          failedFilesList: result.failedFiles || []
        };
      } else {
        // 普通模式下的样式文件处理
        return await this.processStyleFilesNormal();
      }

    } catch (error) {
      console.log(chalk.yellow('⚠️  样式文件迁移失败'));
      return {
        success: false,
        error: error.message,
        totalFiles: 0,
        successFiles: 0,
        failedFiles: 0,
        failedFilesList: []
      };
    }
  }

  /**
   * 普通模式下的样式文件处理
   */
  async processStyleFilesNormal() {
    const styleFiles = await this.findStyleFiles();
    let successCount = 0;
    let failedCount = 0;
    const failedFiles = [];

    for (const file of styleFiles) {
      try {
        await this.processStyleFile(file);
        successCount++;
      } catch (error) {
        failedCount++;
        failedFiles.push({
          file,
          error: error.message,
          errorType: 'style-processing'
        });
      }
    }

    return {
      success: true,
      totalFiles: styleFiles.length,
      successFiles: successCount,
      failedFiles: failedCount,
      failedFilesList: failedFiles
    };
  }

  /**
   * 查找样式文件
   */
  async findStyleFiles() {
    const styleFiles = [];
    const extensions = ['.css', '.scss', '.sass', '.less', '.styl'];
    
    await this.walkDirectory(this.workingPath, (filePath) => {
      if (extensions.some(ext => filePath.endsWith(ext))) {
        styleFiles.push(filePath);
      }
    });

    return styleFiles;
  }

  /**
   * 处理单个样式文件
   */
  async processStyleFile(filePath) {
    // 这里可以添加样式文件的特定处理逻辑
    // 例如：更新 CSS 变量、修复兼容性问题等
    console.log(chalk.gray(`    处理样式文件: ${path.relative(this.workingPath, filePath)}`));
  }

  /**
   * 迁移第三方组件
   */
  async migrateThirdPartyComponents() {
    console.log(chalk.blue('  🔌 迁移第三方组件...'));
    
    try {
      // 这里可以集成第三方组件迁移逻辑
      // 例如：Element UI 到 Element Plus 的迁移
      
      const componentFiles = await this.findComponentFiles();
      let successCount = 0;
      let failedCount = 0;
      const failedFiles = [];

      for (const file of componentFiles) {
        try {
          await this.processThirdPartyComponents(file);
          successCount++;
        } catch (error) {
          failedCount++;
          failedFiles.push({
            file,
            error: error.message,
            errorType: 'third-party-component'
          });
        }
      }

      return {
        success: true,
        totalFiles: componentFiles.length,
        successFiles: successCount,
        failedFiles: failedCount,
        failedFilesList: failedFiles
      };

    } catch (error) {
      console.log(chalk.yellow('⚠️  第三方组件迁移失败'));
      return {
        success: false,
        error: error.message,
        totalFiles: 0,
        successFiles: 0,
        failedFiles: 0,
        failedFilesList: []
      };
    }
  }

  /**
   * 查找组件文件
   */
  async findComponentFiles() {
    const componentFiles = [];
    const extensions = ['.vue', '.js', '.ts'];
    
    await this.walkDirectory(this.workingPath, (filePath) => {
      if (extensions.some(ext => filePath.endsWith(ext))) {
        componentFiles.push(filePath);
      }
    });

    return componentFiles;
  }

  /**
   * 处理文件中的第三方组件
   */
  async processThirdPartyComponents(filePath) {
    // 这里可以添加第三方组件的迁移逻辑
    console.log(chalk.gray(`    检查第三方组件: ${path.relative(this.workingPath, filePath)}`));
  }

  /**
   * 遍历目录
   */
  async walkDirectory(dirPath, callback) {
    try {
      const items = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item.name);
        
        if (item.isDirectory()) {
          // 跳过 node_modules 等目录
          if (!['node_modules', '.git', 'dist', 'build'].includes(item.name)) {
            await this.walkDirectory(fullPath, callback);
          }
        } else if (item.isFile()) {
          callback(fullPath);
        }
      }
    } catch (error) {
      // 忽略无法访问的目录
    }
  }

  /**
   * 获取失败文件列表（供后续阶段使用）
   */
  getFailedFiles() {
    return this.failedFiles;
  }

  /**
   * 判断是否为关键错误
   */
  isCriticalError(error) {
    // 代码迁移失败通常不是关键错误，可以继续后续修复
    return false;
  }
}

module.exports = CodeMigrationPhase;
