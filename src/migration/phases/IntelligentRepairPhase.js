const MigrationPhase = require('../MigrationPhase');
const BuildFixer = require('../../build/buildFixer');
const AIRepairer = require('../../ai/aiRepairer');
const ESLintFixer = require('../../lint/eslintFixer');
const chalk = require('chalk');

/**
 * 智能修复与优化阶段
 * 核心 AI 修复逻辑和迭代修复循环
 */
class IntelligentRepairPhase extends MigrationPhase {
  constructor(projectPath, options = {}) {
    super('智能修复与优化', projectPath, options);
    this.sourceToTargetMode = options.sourceToTargetMode || false;
    this.targetProjectPath = options.targetProjectPath;
    this.workingPath = this.sourceToTargetMode ? this.targetProjectPath : this.projectPath;
    this.maxAttempts = options.maxRepairAttempts || 3;
    this.buildCommand = options.buildCommand || 'npm run build';
  }

  /**
   * 获取阶段依赖
   */
  getDependencies() {
    return ['代码迁移与转换'];
  }

  /**
   * 执行智能修复阶段
   */
  async execute(context) {
    const result = {
      success: false,
      buildSuccess: false,
      repairAttempts: 0,
      aiRepairSuccess: false,
      eslintFixSuccess: false,
      finalBuildSuccess: false,
      errors: [],
      fixedIssues: []
    };

    try {
      console.log(chalk.blue('🤖 开始智能修复循环...'));

      // 获取前一阶段的失败文件
      const failedFiles = this.getFailedFilesFromContext(context);
      
      // 执行迭代修复循环
      const repairResult = await this.executeRepairLoop(failedFiles);
      
      // 更新结果
      Object.assign(result, repairResult);
      
      // 如果构建成功，执行最终优化
      if (result.buildSuccess) {
        console.log(chalk.blue('✨ 执行最终优化...'));
        await this.performFinalOptimizations(result);
      }

      result.success = result.buildSuccess;
      return result;

    } catch (error) {
      result.success = false;
      result.error = error.message;
      result.errors.push(error.message);
      throw error;
    }
  }

  /**
   * 从上下文获取失败文件
   */
  getFailedFilesFromContext(context) {
    const failedFiles = [];
    
    // 从代码迁移阶段获取失败文件
    if (context['代码迁移与转换']?.failedFilesList) {
      failedFiles.push(...context['代码迁移与转换'].failedFilesList);
    }

    return failedFiles;
  }

  /**
   * 执行修复循环
   */
  async executeRepairLoop(initialFailedFiles) {
    const result = {
      buildSuccess: false,
      repairAttempts: 0,
      aiRepairSuccess: false,
      eslintFixSuccess: false,
      errors: [],
      fixedIssues: [],
      repairStrategies: []
    };

    let currentFailedFiles = [...initialFailedFiles];
    let previousErrors = [];
    let stuckCounter = 0; // 检测是否陷入循环

    for (let attempt = 1; attempt <= this.maxAttempts; attempt++) {
      result.repairAttempts = attempt;
      console.log(chalk.blue(`\n🔄 修复尝试 ${attempt}/${this.maxAttempts}...`));

      try {
        // 1. 构建检测
        console.log(chalk.blue('  🏗️  检测构建状态...'));
        const buildResult = await this.attemptBuild();

        if (buildResult.success) {
          result.buildSuccess = true;
          result.fixedIssues.push(`第 ${attempt} 次尝试构建成功`);
          console.log(chalk.green('  ✅ 构建成功！'));
          break;
        } else {
          console.log(chalk.yellow(`  ⚠️  构建失败，发现 ${buildResult.errors.length} 个错误`));

          // 检测是否陷入循环（相同错误重复出现）
          if (this.areErrorsSimilar(buildResult.errors, previousErrors)) {
            stuckCounter++;
            console.log(chalk.yellow(`  ⚠️  检测到相似错误 (${stuckCounter}/${Math.floor(this.maxAttempts/2)})`));

            if (stuckCounter >= Math.floor(this.maxAttempts / 2)) {
              console.log(chalk.yellow('  🔄 切换到替代修复策略...'));
              const alternativeResult = await this.performAlternativeStrategy(buildResult.errors);
              if (alternativeResult.success) {
                result.fixedIssues.push('使用替代策略修复了问题');
                result.repairStrategies.push('alternative');
              }
              stuckCounter = 0; // 重置计数器
            }
          } else {
            stuckCounter = 0; // 重置计数器
          }

          previousErrors = [...buildResult.errors];
          result.errors.push(...buildResult.errors);
        }

        // 2. 选择修复策略
        const strategy = this.selectRepairStrategy(attempt, buildResult.errors, result.repairStrategies);
        result.repairStrategies.push(strategy);

        console.log(chalk.blue(`  📋 使用修复策略: ${strategy}`));

        // 3. 执行修复
        await this.executeRepairStrategy(strategy, buildResult.errors, currentFailedFiles, result);

        // 4. 更新失败文件列表
        currentFailedFiles = await this.updateFailedFilesList(buildResult.errors);

      } catch (error) {
        console.log(chalk.red(`  ❌ 修复尝试 ${attempt} 失败: ${error.message}`));
        result.errors.push(`修复尝试 ${attempt} 失败: ${error.message}`);

        // 智能错误恢复
        const recoveryResult = await this.attemptErrorRecovery(error, attempt);
        if (recoveryResult.recovered) {
          console.log(chalk.green(`  ✅ 错误恢复成功: ${recoveryResult.action}`));
          result.fixedIssues.push(`错误恢复: ${recoveryResult.action}`);
          continue; // 继续下一次尝试
        }

        // 如果是最后一次尝试，抛出错误
        if (attempt === this.maxAttempts) {
          throw error;
        }
      }
    }

    return result;
  }

  /**
   * 尝试构建项目
   */
  async attemptBuild() {
    try {
      const buildFixer = new BuildFixer(this.workingPath, {
        buildCommand: this.buildCommand,
        timeout: 60000, // 60秒超时
        captureOutput: true
      });

      // 只执行构建检测，不执行修复
      const result = await buildFixer.detectBuildErrors();
      
      return {
        success: result.success,
        errors: result.errors || [],
        output: result.output || ''
      };

    } catch (error) {
      return {
        success: false,
        errors: [error.message],
        output: ''
      };
    }
  }

  /**
   * 执行 AI 修复
   */
  async performAIRepair(buildErrors, failedFiles) {
    try {
      const aiRepairer = new AIRepairer({ 
        apiKey: this.options.aiApiKey,
        maxRetries: 2
      });

      if (!aiRepairer.isEnabled()) {
        console.log(chalk.yellow('  ⚠️  AI 修复不可用（缺少 API Key）'));
        return { success: false, fixedFiles: 0 };
      }

      // 合并构建错误和失败文件
      const buildIssues = buildErrors.map(error => ({
        type: 'build_error',
        error: error,
        file: this.extractFileFromError(error)
      })).filter(issue => issue.file !== null); // 过滤掉无法提取文件路径的错误

      const migrationIssues = failedFiles.map(file => ({
        type: 'migration_failure',
        error: file.error,
        file: file.file
      })).filter(issue => issue.file && typeof issue.file === 'string'); // 确保文件路径有效

      const allIssues = [...buildIssues, ...migrationIssues];

      if (allIssues.length === 0) {
        console.log(chalk.yellow('    没有找到可修复的文件路径'));
        return { success: true, fixedFiles: 0 };
      }

      console.log(chalk.blue(`    准备修复 ${allIssues.length} 个问题...`));
      
      const result = await aiRepairer.repairFailedFiles(allIssues, this.workingPath);
      
      return {
        success: result.success || false,
        fixedFiles: result.success || 0,
        details: result
      };

    } catch (error) {
      console.log(chalk.yellow(`  ⚠️  AI 修复失败: ${error.message}`));
      return { success: false, fixedFiles: 0, error: error.message };
    }
  }

  /**
   * 执行 ESLint 修复
   */
  async performESLintFix() {
    try {
      const eslintFixer = new ESLintFixer(this.workingPath);

      // 检查 ESLint 是否可用
      if (!await eslintFixer.isESLintAvailable()) {
        console.log(chalk.yellow('  ⚠️  ESLint 不可用'));
        return { success: false, filesFixed: 0 };
      }

      const result = await eslintFixer.fix();
      
      return {
        success: true,
        filesFixed: result.filesFixed || 0,
        errorsFixed: result.errorsFixed || 0
      };

    } catch (error) {
      console.log(chalk.yellow(`  ⚠️  ESLint 修复失败: ${error.message}`));
      return { success: false, filesFixed: 0, error: error.message };
    }
  }

  /**
   * 更新失败文件列表
   */
  async updateFailedFilesList(buildErrors) {
    const failedFiles = [];
    
    // 从构建错误中提取文件信息
    buildErrors.forEach(error => {
      const file = this.extractFileFromError(error);
      if (file) {
        failedFiles.push({
          file,
          error,
          errorType: 'build_error'
        });
      }
    });

    return failedFiles;
  }

  /**
   * 从错误信息中提取文件路径
   */
  extractFileFromError(error) {
    if (!error || typeof error !== 'string') {
      return null;
    }

    // 尝试从错误信息中提取文件路径
    const patterns = [
      /ERROR in (.+?):/,
      /Module not found: Error: Can't resolve '(.+?)'/,
      /Failed to compile (.+)/,
      /at (.+?):\d+:\d+/,
      /Error in (.+?)$/,
      /File: (.+?)$/,
      /in (.+?\.(?:vue|js|ts|jsx|tsx))/,
      /(.+?\.(?:vue|js|ts|jsx|tsx)):/,
      // Vite 错误模式
      /\[vite\] (.+?) \(/,
      // Webpack 错误模式
      /webpack:\/\/\/(.+?)\?/,
      // ESLint 错误模式
      /(.+?\.(?:vue|js|ts|jsx|tsx))\s+\d+:\d+/
    ];

    for (const pattern of patterns) {
      const match = error.match(pattern);
      if (match && match[1]) {
        let filePath = match[1].trim();

        // 清理路径
        filePath = filePath.replace(/^\.\//, ''); // 移除 ./
        filePath = filePath.replace(/\?.*$/, ''); // 移除查询参数
        filePath = filePath.replace(/^webpack:\/\/\//, ''); // 移除 webpack 前缀

        // 验证是否为有效的文件路径
        if (filePath && filePath.length > 0 && !filePath.includes('node_modules')) {
          return filePath;
        }
      }
    }

    return null;
  }

  /**
   * 执行最终优化
   */
  async performFinalOptimizations(result) {
    try {
      // 1. 代码格式化
      console.log(chalk.blue('  📝 代码格式化...'));
      await this.formatCode();

      // 2. 性能优化建议
      console.log(chalk.blue('  ⚡ 生成性能优化建议...'));
      const optimizations = await this.generateOptimizationSuggestions();
      result.optimizationSuggestions = optimizations;

      // 3. 最终构建验证
      console.log(chalk.blue('  🔍 最终构建验证...'));
      const finalBuild = await this.attemptBuild();
      result.finalBuildSuccess = finalBuild.success;

    } catch (error) {
      console.log(chalk.yellow(`⚠️  最终优化失败: ${error.message}`));
    }
  }

  /**
   * 代码格式化
   */
  async formatCode() {
    // 这里可以集成 Prettier 或其他代码格式化工具
    console.log(chalk.gray('    代码格式化功能待实现'));
  }

  /**
   * 生成优化建议
   */
  async generateOptimizationSuggestions() {
    const suggestions = [
      '考虑使用 Vue 3 的 Composition API 重构复杂组件',
      '检查是否可以移除不必要的依赖',
      '优化打包配置以减少包体积',
      '使用 Vue 3 的新特性如 Teleport、Suspense 等'
    ];

    return suggestions;
  }

  /**
   * 检测错误是否相似
   */
  areErrorsSimilar(currentErrors, previousErrors) {
    if (!previousErrors || previousErrors.length === 0) {
      return false;
    }

    // 简单的相似度检测：检查是否有相同的错误关键词
    const currentKeywords = this.extractErrorKeywords(currentErrors);
    const previousKeywords = this.extractErrorKeywords(previousErrors);

    const commonKeywords = currentKeywords.filter(keyword =>
      previousKeywords.includes(keyword)
    );

    // 如果有超过 50% 的关键词相同，认为是相似错误
    return commonKeywords.length > Math.max(currentKeywords.length, previousKeywords.length) * 0.5;
  }

  /**
   * 提取错误关键词
   */
  extractErrorKeywords(errors) {
    const keywords = [];

    errors.forEach(error => {
      const errorStr = typeof error === 'string' ? error : error.message || '';

      // 提取关键词
      const matches = errorStr.match(/\b(ERROR|Module|plugin|config|webpack|vue|Cannot|Failed|undefined|null)\b/gi);
      if (matches) {
        keywords.push(...matches.map(m => m.toLowerCase()));
      }
    });

    return [...new Set(keywords)]; // 去重
  }

  /**
   * 选择修复策略
   */
  selectRepairStrategy(attempt, errors, previousStrategies) {
    // 第一次尝试：AI 修复
    if (attempt === 1) {
      return 'ai-repair';
    }

    // 第二次尝试：ESLint 修复
    if (attempt === 2) {
      return 'eslint-fix';
    }

    // 第三次尝试：组合策略
    if (attempt === 3) {
      return 'combined';
    }

    // 后续尝试：根据错误类型选择
    const errorTypes = this.analyzeErrorTypes(errors);

    if (errorTypes.includes('webpack') && !previousStrategies.includes('webpack-fix')) {
      return 'webpack-fix';
    }

    if (errorTypes.includes('dependency') && !previousStrategies.includes('dependency-fix')) {
      return 'dependency-fix';
    }

    // 默认策略
    return 'fallback';
  }

  /**
   * 分析错误类型
   */
  analyzeErrorTypes(errors) {
    const types = [];

    errors.forEach(error => {
      const errorStr = typeof error === 'string' ? error : error.message || '';

      if (/webpack|plugin|config\.plugin/i.test(errorStr)) {
        types.push('webpack');
      }

      if (/module not found|cannot resolve/i.test(errorStr)) {
        types.push('dependency');
      }

      if (/vue|component/i.test(errorStr)) {
        types.push('vue');
      }

      if (/eslint|syntax/i.test(errorStr)) {
        types.push('syntax');
      }
    });

    return [...new Set(types)];
  }

  /**
   * 执行修复策略
   */
  async executeRepairStrategy(strategy, errors, failedFiles, result) {
    switch (strategy) {
      case 'ai-repair':
        if (this.options.aiApiKey && !this.options.skipAIRepair) {
          console.log(chalk.blue('  🤖 AI 智能修复...'));
          const aiResult = await this.performAIRepair(errors, failedFiles);
          result.aiRepairSuccess = aiResult.success;
          if (aiResult.fixedFiles > 0) {
            result.fixedIssues.push(`AI 修复了 ${aiResult.fixedFiles} 个文件`);
          }
        }
        break;

      case 'eslint-fix':
        if (!this.options.skipESLint) {
          console.log(chalk.blue('  🔧 ESLint 自动修复...'));
          const eslintResult = await this.performESLintFix();
          result.eslintFixSuccess = eslintResult.success;
          if (eslintResult.filesFixed > 0) {
            result.fixedIssues.push(`ESLint 修复了 ${eslintResult.filesFixed} 个文件`);
          }
        }
        break;

      case 'combined':
        // 组合策略：先 AI 后 ESLint
        await this.executeRepairStrategy('ai-repair', errors, failedFiles, result);
        await this.executeRepairStrategy('eslint-fix', errors, failedFiles, result);
        break;

      case 'webpack-fix':
        console.log(chalk.blue('  ⚙️  Webpack 配置修复...'));
        const webpackResult = await this.performWebpackFix(errors);
        if (webpackResult.success) {
          result.fixedIssues.push('修复了 Webpack 配置问题');
        }
        break;

      case 'dependency-fix':
        console.log(chalk.blue('  📦 依赖问题修复...'));
        const depResult = await this.performDependencyFix(errors);
        if (depResult.success) {
          result.fixedIssues.push('修复了依赖问题');
        }
        break;

      case 'fallback':
        console.log(chalk.blue('  🔄 回退策略...'));
        const fallbackResult = await this.performFallbackStrategy(errors);
        if (fallbackResult.success) {
          result.fixedIssues.push('使用回退策略修复了问题');
        }
        break;
    }
  }

  /**
   * 执行替代策略
   */
  async performAlternativeStrategy(errors) {
    try {
      console.log(chalk.blue('    🔄 尝试替代修复策略...'));

      // 策略1: 删除有问题的配置
      if (errors.some(e => e.includes && e.includes('plugin'))) {
        return await this.removeProblematicPlugins(errors);
      }

      // 策略2: 重置配置文件
      if (errors.some(e => e.includes && e.includes('config'))) {
        return await this.resetConfigFiles();
      }

      // 策略3: 清理缓存
      return await this.clearBuildCache();

    } catch (error) {
      console.log(chalk.yellow(`    ⚠️  替代策略失败: ${error.message}`));
      return { success: false };
    }
  }

  /**
   * 尝试错误恢复
   */
  async attemptErrorRecovery(error, attempt) {
    try {
      // 权限错误恢复
      if (error.message.includes('permission') || error.message.includes('EACCES')) {
        console.log(chalk.blue('    🔧 尝试权限错误恢复...'));
        // 这里可以添加权限修复逻辑
        return { recovered: false, action: '权限错误需要手动处理' };
      }

      // 内存错误恢复
      if (error.message.includes('out of memory') || error.message.includes('ENOMEM')) {
        console.log(chalk.blue('    🔧 尝试内存错误恢复...'));
        // 清理缓存，减少内存使用
        await this.clearBuildCache();
        return { recovered: true, action: '清理缓存以释放内存' };
      }

      // 网络错误恢复
      if (error.message.includes('network') || error.message.includes('timeout')) {
        console.log(chalk.blue('    🔧 尝试网络错误恢复...'));
        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 2000));
        return { recovered: true, action: '等待网络恢复' };
      }

      return { recovered: false, action: '无法自动恢复' };

    } catch (recoveryError) {
      return { recovered: false, action: `恢复失败: ${recoveryError.message}` };
    }
  }

  /**
   * 删除有问题的插件
   */
  async removeProblematicPlugins(errors) {
    // 实现删除有问题插件的逻辑
    console.log(chalk.gray('    删除有问题的插件配置...'));
    return { success: true };
  }

  /**
   * 重置配置文件
   */
  async resetConfigFiles() {
    // 实现重置配置文件的逻辑
    console.log(chalk.gray('    重置配置文件...'));
    return { success: true };
  }

  /**
   * 清理构建缓存
   */
  async clearBuildCache() {
    // 实现清理缓存的逻辑
    console.log(chalk.gray('    清理构建缓存...'));
    return { success: true };
  }

  /**
   * Webpack 配置修复
   */
  async performWebpackFix(errors) {
    // 实现 Webpack 配置修复逻辑
    console.log(chalk.gray('    修复 Webpack 配置...'));
    return { success: true };
  }

  /**
   * 依赖问题修复
   */
  async performDependencyFix(errors) {
    // 实现依赖问题修复逻辑
    console.log(chalk.gray('    修复依赖问题...'));
    return { success: true };
  }

  /**
   * 回退策略
   */
  async performFallbackStrategy(errors) {
    // 实现回退策略逻辑
    console.log(chalk.gray('    执行回退策略...'));
    return { success: true };
  }

  /**
   * 判断是否为关键错误
   */
  isCriticalError(error) {
    // 智能修复阶段的错误通常不是关键的，除非是配置问题
    return error.message.includes('配置') ||
           error.message.includes('权限');
  }
}

module.exports = IntelligentRepairPhase;
