const MigrationPhase = require('../MigrationPhase');
const PackageUpgrader = require('../../dependency/packageUpgrader');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * 依赖升级与映射阶段
 * 负责升级 package.json、处理依赖映射、解决冲突
 */
class DependencyUpgradePhase extends MigrationPhase {
  constructor(projectPath, options = {}) {
    super('依赖升级与映射', projectPath, options);
    this.sourceToTargetMode = options.sourceToTargetMode || false;
    this.targetProjectPath = options.targetProjectPath;
    this.workingPath = this.sourceToTargetMode ? this.targetProjectPath : this.projectPath;
  }

  /**
   * 获取阶段依赖
   */
  getDependencies() {
    return ['项目分析与准备'];
  }

  /**
   * 执行依赖升级阶段
   */
  async execute(context) {
    const result = {
      success: true,
      packageUpgraded: false,
      dependenciesMapped: false,
      configsUpdated: false,
      conflictsResolved: false,
      changes: []
    };

    try {
      // 1. package.json 升级
      console.log(chalk.blue('📦 升级 package.json...'));
      const upgradeResult = await this.upgradePackageJson();
      result.packageUpgraded = upgradeResult.success;
      result.changes.push(...(upgradeResult.changes || []));

      // 2. AI 辅助依赖映射（仅源到目标模式）
      if (this.sourceToTargetMode) {
        console.log(chalk.blue('🔄 处理依赖映射...'));
        const mappingResult = await this.processDependencyMapping();
        result.dependenciesMapped = mappingResult.success;
        result.changes.push(...(mappingResult.changes || []));
      }

      // 3. 配置文件智能更新
      console.log(chalk.blue('⚙️  更新配置文件...'));
      const configResult = await this.updateConfigFiles(context);
      result.configsUpdated = configResult.success;
      result.changes.push(...(configResult.changes || []));

      // 4. 依赖冲突解决
      console.log(chalk.blue('🔧 解决依赖冲突...'));
      const conflictResult = await this.resolveDependencyConflicts();
      result.conflictsResolved = conflictResult.success;
      result.changes.push(...(conflictResult.changes || []));

      return result;

    } catch (error) {
      result.success = false;
      result.error = error.message;
      throw error;
    }
  }

  /**
   * 升级 package.json
   */
  async upgradePackageJson() {
    try {
      const upgrader = new PackageUpgrader(this.workingPath, {
        dryRun: this.options.dryRun,
        verbose: this.options.verbose,
        migrationMode: this.sourceToTargetMode,
        preserveVue3Dependencies: this.sourceToTargetMode
      });

      const result = await upgrader.upgrade();
      
      if (this.options.verbose && result.changes?.length > 0) {
        console.log(chalk.gray('  升级详情:'));
        result.changes.forEach(change => {
          console.log(chalk.gray(`    ${change}`));
        });
      }

      return {
        success: true,
        changes: result.changes || [],
        upgradedPackages: result.upgradedPackages || 0
      };

    } catch (error) {
      console.log(chalk.yellow('⚠️  package.json 升级失败'));
      return {
        success: false,
        error: error.message,
        changes: []
      };
    }
  }

  /**
   * 处理依赖映射
   */
  async processDependencyMapping() {
    if (!this.sourceToTargetMode) {
      return { success: true, changes: [] };
    }

    try {
      // 简化的依赖映射逻辑
      console.log(chalk.gray('   📦 依赖映射功能待实现'));

      return {
        success: true,
        changes: [],
        mappedCount: 0
      };

    } catch (error) {
      console.log(chalk.yellow('⚠️  依赖映射失败'));
      return {
        success: false,
        error: error.message,
        changes: []
      };
    }
  }

  /**
   * 更新配置文件
   */
  async updateConfigFiles(context) {
    const changes = [];
    
    try {
      // 更新 Vue 配置文件
      await this.updateVueConfig(changes);
      
      // 更新 Webpack 配置
      await this.updateWebpackConfig(changes);
      
      // 更新 Vite 配置
      await this.updateViteConfig(changes);
      
      // 更新 TypeScript 配置
      await this.updateTypeScriptConfig(changes);

      return {
        success: true,
        changes,
        updatedConfigs: changes.length
      };

    } catch (error) {
      console.log(chalk.yellow('⚠️  配置文件更新失败'));
      return {
        success: false,
        error: error.message,
        changes
      };
    }
  }

  /**
   * 更新 Vue 配置
   */
  async updateVueConfig(changes) {
    const vueConfigPath = path.join(this.workingPath, 'vue.config.js');
    
    if (await fs.pathExists(vueConfigPath)) {
      try {
        let content = await fs.readFile(vueConfigPath, 'utf8');
        let modified = false;

        // 更新 Vue 3 相关配置
        if (content.includes('runtimeCompiler')) {
          content = content.replace(
            /runtimeCompiler:\s*true/g,
            'runtimeCompiler: true // Vue 3 兼容'
          );
          modified = true;
        }

        if (modified && !this.options.dryRun) {
          await fs.writeFile(vueConfigPath, content);
          changes.push('更新 vue.config.js');
        }

      } catch (error) {
        console.log(chalk.yellow('⚠️  vue.config.js 更新失败'));
      }
    }
  }

  /**
   * 更新 Webpack 配置
   */
  async updateWebpackConfig(changes) {
    const webpackConfigPath = path.join(this.workingPath, 'webpack.config.js');
    
    if (await fs.pathExists(webpackConfigPath)) {
      try {
        let content = await fs.readFile(webpackConfigPath, 'utf8');
        let modified = false;

        // 更新 Vue loader 配置
        if (content.includes('vue-loader')) {
          // 这里可以添加 Vue 3 特定的 webpack 配置更新
          modified = true;
        }

        if (modified && !this.options.dryRun) {
          await fs.writeFile(webpackConfigPath, content);
          changes.push('更新 webpack.config.js');
        }

      } catch (error) {
        console.log(chalk.yellow('⚠️  webpack.config.js 更新失败'));
      }
    }
  }

  /**
   * 更新 Vite 配置
   */
  async updateViteConfig(changes) {
    const viteConfigPath = path.join(this.workingPath, 'vite.config.js');
    
    if (await fs.pathExists(viteConfigPath)) {
      try {
        let content = await fs.readFile(viteConfigPath, 'utf8');
        let modified = false;

        // 确保使用 Vue 3 插件
        if (!content.includes('@vitejs/plugin-vue')) {
          content = content.replace(
            /import\s+{\s*defineConfig\s*}\s+from\s+['"]vite['"]/,
            "import { defineConfig } from 'vite'\nimport vue from '@vitejs/plugin-vue'"
          );
          
          content = content.replace(
            /plugins:\s*\[/,
            'plugins: [\n    vue(),'
          );
          
          modified = true;
        }

        if (modified && !this.options.dryRun) {
          await fs.writeFile(viteConfigPath, content);
          changes.push('更新 vite.config.js');
        }

      } catch (error) {
        console.log(chalk.yellow('⚠️  vite.config.js 更新失败'));
      }
    }
  }

  /**
   * 更新 TypeScript 配置
   */
  async updateTypeScriptConfig(changes) {
    const tsConfigPath = path.join(this.workingPath, 'tsconfig.json');
    
    if (await fs.pathExists(tsConfigPath)) {
      try {
        const tsConfig = await fs.readJson(tsConfigPath);
        let modified = false;

        // 更新 TypeScript 配置以支持 Vue 3
        if (tsConfig.compilerOptions) {
          if (!tsConfig.compilerOptions.moduleResolution) {
            tsConfig.compilerOptions.moduleResolution = 'node';
            modified = true;
          }
          
          if (!tsConfig.compilerOptions.jsx) {
            tsConfig.compilerOptions.jsx = 'preserve';
            modified = true;
          }
        }

        if (modified && !this.options.dryRun) {
          await fs.writeJson(tsConfigPath, tsConfig, { spaces: 2 });
          changes.push('更新 tsconfig.json');
        }

      } catch (error) {
        console.log(chalk.yellow('⚠️  tsconfig.json 更新失败'));
      }
    }
  }

  /**
   * 解决依赖冲突
   */
  async resolveDependencyConflicts() {
    const changes = [];
    
    try {
      const packageJsonPath = path.join(this.workingPath, 'package.json');
      
      if (await fs.pathExists(packageJsonPath)) {
        const packageJson = await fs.readJson(packageJsonPath);
        let modified = false;

        // 检查并解决已知冲突
        const conflicts = await this.detectConflicts(packageJson);
        
        for (const conflict of conflicts) {
          const resolution = await this.resolveConflict(conflict, packageJson);
          if (resolution.resolved) {
            changes.push(resolution.description);
            modified = true;
          }
        }

        if (modified && !this.options.dryRun) {
          await fs.writeJson(packageJsonPath, packageJson, { spaces: 2 });
        }
      }

      return {
        success: true,
        changes,
        conflictsResolved: changes.length
      };

    } catch (error) {
      console.log(chalk.yellow('⚠️  依赖冲突解决失败'));
      return {
        success: false,
        error: error.message,
        changes
      };
    }
  }

  /**
   * 检测依赖冲突
   */
  async detectConflicts(packageJson) {
    const conflicts = [];
    const allDeps = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies
    };

    // 检查 Vue 2 和 Vue 3 共存
    if (allDeps.vue && allDeps.vue.startsWith('^2.')) {
      conflicts.push({
        type: 'version_conflict',
        package: 'vue',
        description: 'Vue 2 版本需要升级到 Vue 3'
      });
    }

    // 检查不兼容的依赖
    const incompatibleDeps = {
      'vue-template-compiler': '@vue/compiler-sfc',
      'vue-property-decorator': null, // 需要移除
      'vue-class-component': null
    };

    Object.entries(incompatibleDeps).forEach(([oldDep, newDep]) => {
      if (allDeps[oldDep]) {
        conflicts.push({
          type: 'incompatible_dependency',
          package: oldDep,
          replacement: newDep,
          description: newDep ? 
            `${oldDep} 需要替换为 ${newDep}` : 
            `${oldDep} 需要移除`
        });
      }
    });

    return conflicts;
  }

  /**
   * 解决单个冲突
   */
  async resolveConflict(conflict, packageJson) {
    switch (conflict.type) {
      case 'version_conflict':
        return this.resolveVersionConflict(conflict, packageJson);
      
      case 'incompatible_dependency':
        return this.resolveIncompatibleDependency(conflict, packageJson);
      
      default:
        return { resolved: false };
    }
  }

  /**
   * 解决版本冲突
   */
  async resolveVersionConflict(conflict, packageJson) {
    if (conflict.package === 'vue') {
      // Vue 2 升级到 Vue 3 的逻辑已在 PackageUpgrader 中处理
      return { resolved: true, description: 'Vue 版本冲突已解决' };
    }
    
    return { resolved: false };
  }

  /**
   * 解决不兼容依赖
   */
  async resolveIncompatibleDependency(conflict, packageJson) {
    const { package: pkg, replacement } = conflict;
    
    // 从依赖中移除
    if (packageJson.dependencies?.[pkg]) {
      delete packageJson.dependencies[pkg];
    }
    if (packageJson.devDependencies?.[pkg]) {
      delete packageJson.devDependencies[pkg];
    }
    
    // 如果有替换依赖，添加它
    if (replacement) {
      if (!packageJson.devDependencies) {
        packageJson.devDependencies = {};
      }
      packageJson.devDependencies[replacement] = '^3.0.0';
    }
    
    return { 
      resolved: true, 
      description: replacement ? 
        `移除 ${pkg}，添加 ${replacement}` : 
        `移除 ${pkg}`
    };
  }

  /**
   * 判断是否为关键错误
   */
  isCriticalError(error) {
    // 依赖升级失败通常是关键的
    return error.message.includes('package.json') || 
           error.message.includes('dependency');
  }
}

module.exports = DependencyUpgradePhase;
