const MigrationPhase = require('../MigrationPhase');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { spawn } = require('child_process');

/**
 * 验证与完善阶段
 * 最终验证、测试和报告生成
 */
class ValidationPhase extends MigrationPhase {
  constructor(projectPath, options = {}) {
    super('验证与完善', projectPath, options);
    this.sourceToTargetMode = options.sourceToTargetMode || false;
    this.targetProjectPath = options.targetProjectPath;
    this.workingPath = this.sourceToTargetMode ? this.targetProjectPath : this.projectPath;
    this.buildCommand = options.buildCommand || 'npm run build';
    this.testCommand = options.testCommand || 'npm test';
  }

  /**
   * 获取阶段依赖
   */
  getDependencies() {
    return ['智能修复与优化'];
  }

  /**
   * 获取关键依赖
   * 验证阶段可以在智能修复部分失败的情况下执行
   */
  getCriticalDependencies() {
    return []; // 验证阶段没有关键依赖，可以在任何情况下执行
  }

  /**
   * 执行验证完善阶段
   */
  async execute(context) {
    const result = {
      success: true,
      finalBuildSuccess: false,
      testResults: null,
      migrationReport: null,
      recommendations: [],
      validationErrors: []
    };

    try {
      // 1. 最终构建验证
      console.log(chalk.blue('🏗️  最终构建验证...'));
      const buildResult = await this.performFinalBuildCheck();
      result.finalBuildSuccess = buildResult.success;
      if (!buildResult.success) {
        result.validationErrors.push(...buildResult.errors);
      }

      // 2. 测试运行检查
      console.log(chalk.blue('🧪 测试运行检查...'));
      const testResult = await this.runTestSuite();
      result.testResults = testResult;

      // 3. 生成迁移报告
      console.log(chalk.blue('📄 生成迁移报告...'));
      const reportResult = await this.generateMigrationReport(context);
      result.migrationReport = reportResult;

      // 4. 生成后续建议
      console.log(chalk.blue('💡 生成后续建议...'));
      const recommendations = await this.generateRecommendations(context, result);
      result.recommendations = recommendations;

      // 5. 项目健康检查
      console.log(chalk.blue('🔍 项目健康检查...'));
      const healthCheck = await this.performHealthCheck();
      result.healthCheck = healthCheck;

      return result;

    } catch (error) {
      result.success = false;
      result.error = error.message;
      result.validationErrors.push(error.message);
      throw error;
    }
  }

  /**
   * 执行最终构建检查
   */
  async performFinalBuildCheck() {
    try {
      console.log(chalk.gray('  执行最终构建...'));
      
      const buildResult = await this.runCommand(this.buildCommand, {
        cwd: this.workingPath,
        timeout: 120000 // 2分钟超时
      });

      if (buildResult.code === 0) {
        console.log(chalk.green('  ✅ 最终构建成功'));
        return { success: true, output: buildResult.output };
      } else {
        console.log(chalk.red('  ❌ 最终构建失败'));
        return { 
          success: false, 
          errors: this.parseErrorsFromOutput(buildResult.output),
          output: buildResult.output 
        };
      }

    } catch (error) {
      console.log(chalk.red(`  ❌ 构建检查失败: ${error.message}`));
      return { 
        success: false, 
        errors: [error.message],
        output: '' 
      };
    }
  }

  /**
   * 运行测试套件
   */
  async runTestSuite() {
    try {
      // 检查是否有测试配置
      const hasTests = await this.checkTestConfiguration();
      
      if (!hasTests) {
        console.log(chalk.yellow('  ⚠️  未发现测试配置，跳过测试'));
        return {
          skipped: true,
          reason: '未发现测试配置'
        };
      }

      console.log(chalk.gray('  运行测试套件...'));
      
      const testResult = await this.runCommand(this.testCommand, {
        cwd: this.workingPath,
        timeout: 180000 // 3分钟超时
      });

      const result = {
        success: testResult.code === 0,
        output: testResult.output,
        exitCode: testResult.code
      };

      if (result.success) {
        console.log(chalk.green('  ✅ 测试通过'));
      } else {
        console.log(chalk.yellow('  ⚠️  测试失败或有警告'));
      }

      return result;

    } catch (error) {
      console.log(chalk.yellow(`  ⚠️  测试运行失败: ${error.message}`));
      return {
        success: false,
        error: error.message,
        output: ''
      };
    }
  }

  /**
   * 检查测试配置
   */
  async checkTestConfiguration() {
    const testFiles = [
      'jest.config.js',
      'jest.config.json',
      'vitest.config.js',
      'vitest.config.ts',
      'cypress.json',
      'cypress.config.js'
    ];

    for (const file of testFiles) {
      if (await fs.pathExists(path.join(this.workingPath, file))) {
        return true;
      }
    }

    // 检查 package.json 中的测试脚本
    const packageJsonPath = path.join(this.workingPath, 'package.json');
    if (await fs.pathExists(packageJsonPath)) {
      const packageJson = await fs.readJson(packageJsonPath);
      return !!(packageJson.scripts?.test || packageJson.scripts?.['test:unit']);
    }

    return false;
  }

  /**
   * 生成迁移报告
   */
  async generateMigrationReport(context) {
    try {
      const reportPath = path.join(this.workingPath, 'migration-report.json');
      
      const report = {
        timestamp: new Date().toISOString(),
        projectPath: this.workingPath,
        migrationMode: this.sourceToTargetMode ? 'source-to-target' : 'in-place',
        phases: this.extractPhaseResults(context),
        summary: this.generateSummary(context),
        statistics: this.generateStatistics(context),
        issues: this.extractIssues(context),
        recommendations: []
      };

      if (!this.options.dryRun) {
        await fs.writeJson(reportPath, report, { spaces: 2 });
        console.log(chalk.green(`  ✅ 迁移报告已生成: ${reportPath}`));
      }

      // 同时生成 Markdown 格式的报告
      await this.generateMarkdownReport(report);

      return {
        success: true,
        reportPath,
        report
      };

    } catch (error) {
      console.log(chalk.yellow(`  ⚠️  报告生成失败: ${error.message}`));
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成 Markdown 报告
   */
  async generateMarkdownReport(report) {
    try {
      const markdownPath = path.join(this.workingPath, 'migration-report.md');
      
      const markdown = this.generateMarkdownContent(report);
      
      if (!this.options.dryRun) {
        await fs.writeFile(markdownPath, markdown);
        console.log(chalk.green(`  ✅ Markdown 报告已生成: ${markdownPath}`));
      }

    } catch (error) {
      console.log(chalk.yellow(`  ⚠️  Markdown 报告生成失败: ${error.message}`));
    }
  }

  /**
   * 生成 Markdown 内容
   */
  generateMarkdownContent(report) {
    return `# Vue 2 到 Vue 3 迁移报告

## 迁移概览

- **迁移时间**: ${new Date(report.timestamp).toLocaleString()}
- **项目路径**: ${report.projectPath}
- **迁移模式**: ${report.migrationMode}
- **总体状态**: ${report.summary.success ? '✅ 成功' : '❌ 失败'}

## 阶段执行结果

${Object.entries(report.phases).map(([phase, result]) => 
  `### ${phase}\n- 状态: ${result.success ? '✅ 成功' : '❌ 失败'}\n- 耗时: ${result.duration || 0}ms\n`
).join('\n')}

## 统计信息

- **总耗时**: ${report.statistics.totalDuration}ms
- **完成阶段**: ${report.statistics.completedPhases}
- **失败阶段**: ${report.statistics.failedPhases}

## 发现的问题

${report.issues.map(issue => `- ${issue}`).join('\n')}

## 后续建议

${report.recommendations.map(rec => `- ${rec}`).join('\n')}

---
*此报告由 Vue 迁移工具自动生成*
`;
  }

  /**
   * 提取阶段结果
   */
  extractPhaseResults(context) {
    const phases = {};
    
    Object.keys(context).forEach(key => {
      if (typeof context[key] === 'object' && context[key].success !== undefined) {
        phases[key] = context[key];
      }
    });

    return phases;
  }

  /**
   * 生成摘要
   */
  generateSummary(context) {
    const phases = this.extractPhaseResults(context);
    const totalPhases = Object.keys(phases).length;
    const successfulPhases = Object.values(phases).filter(p => p.success).length;
    
    return {
      success: successfulPhases === totalPhases,
      totalPhases,
      successfulPhases,
      failedPhases: totalPhases - successfulPhases
    };
  }

  /**
   * 生成统计信息
   */
  generateStatistics(context) {
    const phases = this.extractPhaseResults(context);
    
    return {
      totalDuration: Object.values(phases).reduce((sum, phase) => sum + (phase.duration || 0), 0),
      completedPhases: Object.values(phases).filter(p => p.success).length,
      failedPhases: Object.values(phases).filter(p => !p.success).length,
      totalFiles: this.extractTotalFiles(context),
      modifiedFiles: this.extractModifiedFiles(context)
    };
  }

  /**
   * 提取问题列表
   */
  extractIssues(context) {
    const issues = [];
    
    Object.values(context).forEach(result => {
      if (result.errors) {
        issues.push(...result.errors);
      }
      if (result.validationErrors) {
        issues.push(...result.validationErrors);
      }
    });

    return issues;
  }

  /**
   * 提取文件统计
   */
  extractTotalFiles(context) {
    const codeMigration = context['代码迁移与转换'];
    return codeMigration?.totalFiles || 0;
  }

  /**
   * 提取修改文件数
   */
  extractModifiedFiles(context) {
    const codeMigration = context['代码迁移与转换'];
    return codeMigration?.successFiles || 0;
  }

  /**
   * 生成后续建议
   */
  async generateRecommendations(context, validationResult) {
    const recommendations = [];

    // 基于验证结果生成建议
    if (!validationResult.finalBuildSuccess) {
      recommendations.push('修复剩余的构建错误');
    }

    if (validationResult.testResults && !validationResult.testResults.success) {
      recommendations.push('修复失败的测试用例');
    }

    // 基于迁移结果生成建议
    const codeMigration = context['代码迁移与转换'];
    if (codeMigration?.failedFiles > 0) {
      recommendations.push('手动检查迁移失败的文件');
    }

    // 通用建议
    recommendations.push(
      '运行 npm install 安装新依赖',
      '更新文档和部署配置',
      '进行全面的功能测试',
      '考虑使用 Vue 3 的新特性优化代码'
    );

    return recommendations;
  }

  /**
   * 执行项目健康检查
   */
  async performHealthCheck() {
    const checks = {
      packageJsonValid: false,
      dependenciesInstalled: false,
      configFilesValid: false,
      sourceFilesAccessible: false
    };

    try {
      // 检查 package.json
      const packageJsonPath = path.join(this.workingPath, 'package.json');
      if (await fs.pathExists(packageJsonPath)) {
        await fs.readJson(packageJsonPath);
        checks.packageJsonValid = true;
      }

      // 检查依赖是否安装
      const nodeModulesPath = path.join(this.workingPath, 'node_modules');
      checks.dependenciesInstalled = await fs.pathExists(nodeModulesPath);

      // 检查源文件
      const srcPath = path.join(this.workingPath, 'src');
      checks.sourceFilesAccessible = await fs.pathExists(srcPath);

      // 检查配置文件
      checks.configFilesValid = await this.validateConfigFiles();

    } catch (error) {
      console.log(chalk.yellow(`⚠️  健康检查失败: ${error.message}`));
    }

    return checks;
  }

  /**
   * 验证配置文件
   */
  async validateConfigFiles() {
    const configFiles = ['vue.config.js', 'vite.config.js', 'webpack.config.js'];
    
    for (const file of configFiles) {
      const filePath = path.join(this.workingPath, file);
      if (await fs.pathExists(filePath)) {
        try {
          // 简单的语法检查
          const content = await fs.readFile(filePath, 'utf8');
          if (content.trim().length > 0) {
            return true;
          }
        } catch (error) {
          return false;
        }
      }
    }

    return true; // 如果没有配置文件，认为是有效的
  }

  /**
   * 运行命令
   */
  async runCommand(command, options = {}) {
    return new Promise((resolve) => {
      const [cmd, ...args] = command.split(' ');
      const child = spawn(cmd, args, {
        cwd: options.cwd || this.workingPath,
        stdio: 'pipe',
        shell: true
      });

      let output = '';
      let errorOutput = '';

      child.stdout?.on('data', (data) => {
        output += data.toString();
      });

      child.stderr?.on('data', (data) => {
        errorOutput += data.toString();
      });

      const timeout = setTimeout(() => {
        child.kill();
        resolve({
          code: -1,
          output: output + errorOutput,
          error: 'Command timeout'
        });
      }, options.timeout || 60000);

      child.on('close', (code) => {
        clearTimeout(timeout);
        resolve({
          code,
          output: output + errorOutput
        });
      });
    });
  }

  /**
   * 从输出中解析错误
   */
  parseErrorsFromOutput(output) {
    const errors = [];
    const lines = output.split('\n');
    
    lines.forEach(line => {
      if (line.includes('ERROR') || line.includes('Error:') || line.includes('Failed')) {
        errors.push(line.trim());
      }
    });

    return errors;
  }

  /**
   * 判断是否为关键错误
   */
  isCriticalError(error) {
    // 验证阶段的错误通常不是关键的
    return false;
  }
}

module.exports = ValidationPhase;
