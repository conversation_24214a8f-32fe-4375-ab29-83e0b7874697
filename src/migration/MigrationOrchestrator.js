const chalk = require('chalk');
const path = require('path');

/**
 * 迁移协调器
 * 负责协调各个迁移阶段的执行
 */
class MigrationOrchestrator {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = options;
    this.phases = [];
    this.context = {};
    this.stats = {
      startTime: null,
      endTime: null,
      totalDuration: 0,
      completedPhases: 0,
      failedPhases: 0,
      skippedPhases: 0,
      phaseResults: {}
    };
  }

  /**
   * 注册迁移阶段
   * @param {MigrationPhase} phase 
   */
  registerPhase(phase) {
    this.phases.push(phase);
  }

  /**
   * 批量注册阶段
   * @param {Array} phases 
   */
  registerPhases(phases) {
    phases.forEach(phase => this.registerPhase(phase));
  }

  /**
   * 执行所有迁移阶段
   */
  async execute() {
    this.stats.startTime = Date.now();
    
    console.log(chalk.bold.blue('\n🚀 Vue 2 到 Vue 3 迁移工具 - 阶段化执行\n'));
    console.log(chalk.gray(`项目路径: ${this.projectPath}`));
    console.log(chalk.gray(`开始时间: ${new Date().toLocaleString()}`));
    console.log(chalk.gray(`总阶段数: ${this.phases.length}\n`));

    try {
      // 验证阶段依赖关系
      this.validatePhaseDependencies();

      // 按顺序执行各个阶段
      for (let i = 0; i < this.phases.length; i++) {
        const phase = this.phases[i];
        const phaseNumber = i + 1;
        
        console.log(chalk.blue(`\n📋 阶段 ${phaseNumber}/${this.phases.length}: ${phase.name}`));
        
        try {
          // 检查是否可以跳过
          if (phase.canSkip(this.context)) {
            const skipResult = phase.skip();
            this.handlePhaseResult(phase.name, skipResult);
            this.stats.skippedPhases++;
            continue;
          }

          // 验证上下文（使用新的灵活验证）
          try {
            phase.validateContext(this.context);
          } catch (dependencyError) {
            // 检查是否可以在部分失败的情况下执行
            if (phase.canExecuteWithPartialFailure && phase.canExecuteWithPartialFailure(this.context)) {
              console.log(chalk.yellow(`⚠️  ${phase.name} 的依赖部分失败，但尝试继续执行`));
            } else {
              throw dependencyError;
            }
          }

          // 执行阶段
          const result = await phase.start(this.context);
          this.handlePhaseResult(phase.name, result);

          if (result.success !== false) {
            this.stats.completedPhases++;
          } else {
            this.stats.failedPhases++;
          }

        } catch (error) {
          console.error(chalk.red(`❌ 阶段 ${phase.name} 执行失败:`), error.message);
          this.stats.failedPhases++;

          // 如果是关键阶段失败，停止执行
          if (this.isCriticalPhase(phase)) {
            throw error;
          }

          // 非关键阶段失败，记录错误但继续执行
          console.log(chalk.yellow(`⚠️  非关键阶段失败，继续执行后续阶段`));
        }
      }

      this.stats.endTime = Date.now();
      this.stats.totalDuration = this.stats.endTime - this.stats.startTime;
      
      // 打印最终统计
      this.printFinalStats();
      
      return {
        success: this.stats.failedPhases === 0,
        stats: this.stats,
        context: this.context
      };
      
    } catch (error) {
      this.stats.endTime = Date.now();
      this.stats.totalDuration = this.stats.endTime - this.stats.startTime;
      
      console.error(chalk.red('\n❌ 迁移执行失败:'), error.message);
      throw error;
    }
  }

  /**
   * 处理阶段结果
   * @param {string} phaseName 
   * @param {Object} result 
   */
  handlePhaseResult(phaseName, result) {
    // 将结果保存到上下文中，供后续阶段使用
    this.context[phaseName] = result;
    this.stats.phaseResults[phaseName] = result;
    
    // 如果结果包含需要传递的数据，添加到上下文
    if (result && typeof result === 'object') {
      Object.keys(result).forEach(key => {
        if (key !== 'success' && key !== 'phase') {
          this.context[key] = result[key];
        }
      });
    }
  }

  /**
   * 验证阶段依赖关系
   */
  validatePhaseDependencies() {
    const phaseNames = this.phases.map(p => p.name);
    
    for (const phase of this.phases) {
      const dependencies = phase.getDependencies();
      for (const dep of dependencies) {
        if (!phaseNames.includes(dep)) {
          throw new Error(`阶段 ${phase.name} 依赖的阶段 ${dep} 未注册`);
        }
        
        // 检查依赖顺序
        const depIndex = phaseNames.indexOf(dep);
        const phaseIndex = phaseNames.indexOf(phase.name);
        if (depIndex >= phaseIndex) {
          throw new Error(`阶段 ${phase.name} 的依赖 ${dep} 必须在其之前执行`);
        }
      }
    }
  }

  /**
   * 判断是否为关键阶段
   * @param {MigrationPhase} phase 
   * @returns {boolean}
   */
  isCriticalPhase(phase) {
    // 可以根据阶段名称或类型判断
    const criticalPhases = ['项目分析与准备', '依赖升级与映射'];
    return criticalPhases.includes(phase.name);
  }

  /**
   * 打印最终统计
   */
  printFinalStats() {
    const duration = Math.round(this.stats.totalDuration / 1000);
    
    console.log('\n' + chalk.bold.green('🎉 迁移执行完成!'));
    console.log('\n' + chalk.bold('📊 执行统计:'));
    console.log(`总耗时: ${duration} 秒`);
    console.log(`完成阶段: ${this.stats.completedPhases}/${this.phases.length}`);
    console.log(`失败阶段: ${this.stats.failedPhases}`);
    console.log(`跳过阶段: ${this.stats.skippedPhases}`);
    
    // 显示各阶段耗时
    console.log('\n' + chalk.bold('⏱️  各阶段耗时:'));
    this.phases.forEach(phase => {
      const stats = phase.getStats();
      const status = stats.success ? '✅' : '❌';
      const duration = Math.round(stats.duration / 1000);
      console.log(`${status} ${phase.name}: ${duration}s`);
    });
  }

  /**
   * 获取执行上下文
   */
  getContext() {
    return this.context;
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return this.stats;
  }
}

module.exports = MigrationOrchestrator;
