# BuildFixAgent 重构计划

## 当前问题分析

### 1. 代码结构问题
- **单一类过于庞大**: `BuildFixAgent.js` 有1502行代码，职责过多
- **方法职责不清**: 混合了AI调用、工具执行、错误分析、文件操作等多种职责
- **命名不够语义化**: 如 `fixSingleFile`、`generateToolCalls` 等方法名不够直观
- **深度嵌套**: 方法内部逻辑复杂，嵌套层次过深

### 2. 目录结构问题
- **AI相关类分散**: `BuildFixAgent`、`ErrorAnalyzer`、`ToolExecutor` 都在ai目录下，但职责不同
- **缺少领域分层**: 没有按照业务领域进行合理分层
- **工具类混杂**: 工具类和业务类混在一起

### 3. 可维护性问题
- **硬编码过多**: 提示词模板、配置参数等硬编码在类中
- **测试困难**: 大类难以进行单元测试
- **扩展性差**: 新增修复策略需要修改核心类

## 重构目标

### 1. 语义化和可读性
- 使用更直观的类名和方法名
- 提取业务概念，形成清晰的领域模型
- 减少方法复杂度，提高代码可读性

### 2. 职责分离
- 按照单一职责原则拆分大类
- 明确各组件的边界和职责
- 提高代码的内聚性，降低耦合度

### 3. 可维护性
- 提取配置和模板，便于维护
- 增强可测试性
- 提高扩展性，支持插件化架构

## 重构方案

### 阶段一：目录结构重组

```
src/
├── core/                           # 核心领域模型
│   ├── build-fix/                  # 构建修复领域
│   │   ├── BuildFixOrchestrator.js # 构建修复编排器（原BuildFixAgent核心逻辑）
│   │   ├── FixSession.js           # 修复会话管理
│   │   ├── FixAttempt.js           # 单次修复尝试
│   │   └── FixResult.js            # 修复结果模型
│   ├── error-analysis/             # 错误分析领域
│   │   ├── ErrorAnalyzer.js        # 错误分析器（重构现有）
│   │   ├── ErrorClassifier.js      # 错误分类器
│   │   └── ErrorPattern.js         # 错误模式匹配
│   └── file-operations/            # 文件操作领域
│       ├── FileReader.js           # 文件读取器
│       ├── FileWriter.js           # 文件写入器
│       └── FileValidator.js        # 文件验证器
├── services/                       # 服务层
│   ├── ai/                         # AI服务
│   │   ├── AIService.js            # AI服务基类（保持现有）
│   │   ├── PromptBuilder.js        # 提示词构建器
│   │   ├── ResponseParser.js       # 响应解析器
│   │   └── ConversationManager.js  # 对话管理器
│   ├── tools/                      # 工具服务
│   │   ├── ToolRegistry.js         # 工具注册表
│   │   ├── ToolExecutor.js         # 工具执行器（重构现有）
│   │   └── tools/                  # 具体工具实现
│   │       ├── FileReadTool.js
│   │       ├── FileWriteTool.js
│   │       └── DirectoryListTool.js
│   └── logging/                    # 日志服务
│       ├── LogManager.js           # 日志管理器
│       ├── SessionLogger.js        # 会话日志记录器
│       └── StatisticsCollector.js  # 统计信息收集器
├── strategies/                     # 策略模式实现
│   ├── fix-strategies/             # 修复策略
│   │   ├── FixStrategyFactory.js   # 修复策略工厂
│   │   ├── TwoPhaseFixStrategy.js  # 两阶段修复策略
│   │   ├── DirectFixStrategy.js    # 直接修复策略
│   │   └── ContextAwareFixStrategy.js # 上下文感知修复策略
│   └── prompt-strategies/          # 提示词策略
│       ├── WebpackConfigPromptStrategy.js
│       ├── VueFilePromptStrategy.js
│       └── GeneralFixPromptStrategy.js
├── templates/                      # 模板和配置
│   ├── prompts/                    # 提示词模板
│   │   ├── tool-call-prompts.js
│   │   ├── fix-prompts.js
│   │   └── webpack-prompts.js
│   └── configs/                    # 配置模板
│       └── fix-configs.js
└── utils/                          # 工具类
    ├── validation/                 # 验证工具
    │   ├── ContentValidator.js
    │   └── StructureValidator.js
    └── formatting/                 # 格式化工具
        ├── CodeFormatter.js
        └── OutputFormatter.js
```

### 阶段二：核心类重构

#### 1. BuildFixOrchestrator（替代BuildFixAgent）

```javascript
/**
 * 构建修复编排器
 * 负责协调整个修复流程，但不直接执行具体操作
 */
class BuildFixOrchestrator {
  constructor(projectPath, options = {}) {
    this.session = new FixSession(projectPath, options);
    this.errorAnalyzer = new ErrorAnalyzer(projectPath, options);
    this.strategyFactory = new FixStrategyFactory(options);
    this.conversationManager = new ConversationManager(options);
  }

  async orchestrateFixProcess(buildOutput) {
    // 编排修复流程，委托给具体的策略和服务
  }

  async analyzeAndSelectFiles(buildOutput) {
    // 分析错误并选择需要修复的文件
  }

  async executeFixStrategy(filesToFix, buildOutput) {
    // 执行修复策略
  }
}
```

#### 2. FixSession（会话管理）

```javascript
/**
 * 修复会话管理器
 * 负责管理整个修复会话的状态和历史
 */
class FixSession {
  constructor(projectPath, options) {
    this.projectPath = projectPath;
    this.options = options;
    this.attempts = [];
    this.statistics = new StatisticsCollector();
    this.logger = new SessionLogger(options.logDir);
  }

  createAttempt(attemptNumber) {
    const attempt = new FixAttempt(attemptNumber, this.projectPath);
    this.attempts.push(attempt);
    return attempt;
  }

  isRepeatingAttempt(buildOutput) {
    // 检查是否重复尝试
  }

  generateSessionSummary() {
    // 生成会话摘要
  }
}
```

#### 3. TwoPhaseFixStrategy（两阶段修复策略）

```javascript
/**
 * 两阶段修复策略
 * 第一阶段：分析错误，生成工具调用
 * 第二阶段：基于上下文，生成修复代码
 */
class TwoPhaseFixStrategy {
  constructor(conversationManager, toolExecutor) {
    this.conversationManager = conversationManager;
    this.toolExecutor = toolExecutor;
    this.promptBuilder = new PromptBuilder();
    this.responseParser = new ResponseParser();
  }

  async executeFixForFile(filePath, fileContent, buildOutput, context) {
    // 第一阶段：生成工具调用
    const toolCalls = await this.generateToolCalls(filePath, buildOutput, context);
    
    // 执行工具调用，收集上下文
    const contextFiles = await this.toolExecutor.executeTools(toolCalls);
    
    // 第二阶段：基于上下文生成修复
    const fixResult = await this.generateFixWithContext(
      filePath, fileContent, buildOutput, contextFiles, context
    );
    
    return fixResult;
  }

  async generateToolCalls(filePath, buildOutput, context) {
    const prompt = this.promptBuilder.buildToolCallPrompt(filePath, buildOutput, context);
    const response = await this.conversationManager.sendMessage(prompt);
    return this.responseParser.parseToolCalls(response);
  }

  async generateFixWithContext(filePath, fileContent, buildOutput, contextFiles, context) {
    const prompt = this.promptBuilder.buildFixPrompt(
      filePath, fileContent, buildOutput, contextFiles, context
    );
    const response = await this.conversationManager.sendMessage(prompt);
    return this.responseParser.parseFixResponse(response, fileContent);
  }
}
```

### 阶段三：服务层重构

#### 1. PromptBuilder（提示词构建器）

```javascript
/**
 * 提示词构建器
 * 负责根据不同场景构建合适的提示词
 */
class PromptBuilder {
  constructor() {
    this.templates = require('../templates/prompts');
    this.strategies = new Map();
    this.registerStrategies();
  }

  registerStrategies() {
    this.strategies.set('webpack', new WebpackConfigPromptStrategy());
    this.strategies.set('vue', new VueFilePromptStrategy());
    this.strategies.set('general', new GeneralFixPromptStrategy());
  }

  buildToolCallPrompt(filePath, buildOutput, context) {
    const strategy = this.selectStrategy(filePath);
    return strategy.buildToolCallPrompt(filePath, buildOutput, context);
  }

  buildFixPrompt(filePath, fileContent, buildOutput, contextFiles, context) {
    const strategy = this.selectStrategy(filePath);
    return strategy.buildFixPrompt(filePath, fileContent, buildOutput, contextFiles, context);
  }

  selectStrategy(filePath) {
    if (filePath.includes('webpack')) return this.strategies.get('webpack');
    if (filePath.endsWith('.vue')) return this.strategies.get('vue');
    return this.strategies.get('general');
  }
}
```

#### 2. ConversationManager（对话管理器）

```javascript
/**
 * 对话管理器
 * 负责管理与AI的对话，包括上下文维护和重试逻辑
 */
class ConversationManager {
  constructor(options) {
    this.aiService = new AIService(options);
    this.conversationHistory = [];
    this.options = options;
  }

  async sendMessage(prompt, context = {}) {
    const message = {
      prompt,
      context,
      timestamp: Date.now()
    };

    try {
      const response = await this.aiService.callAI(prompt, context);
      this.conversationHistory.push({ message, response, success: true });
      return response;
    } catch (error) {
      this.conversationHistory.push({ message, error, success: false });
      throw error;
    }
  }

  getConversationHistory() {
    return this.conversationHistory;
  }

  clearHistory() {
    this.conversationHistory = [];
  }
}
```

### 阶段四：配置和模板外化

#### 1. 提示词模板外化

```javascript
// templates/prompts/tool-call-prompts.js
module.exports = {
  WEBPACK_TOOL_CALL_TEMPLATE: `
    分析以下webpack配置错误，确定需要读取哪些相关文件来理解上下文：
    
    错误信息：
    {buildOutput}
    
    目标文件：{filePath}
    
    请生成工具调用来读取相关文件...
  `,
  
  VUE_TOOL_CALL_TEMPLATE: `
    分析以下Vue文件错误，确定需要读取哪些相关文件：
    
    错误信息：
    {buildOutput}
    
    目标文件：{filePath}
    
    请生成工具调用...
  `
};
```

#### 2. 修复配置外化

```javascript
// templates/configs/fix-configs.js
module.exports = {
  DEFAULT_OPTIONS: {
    maxAttempts: 6,
    dryRun: false,
    verbose: false,
    strategy: 'two-phase'
  },
  
  VALIDATION_RULES: {
    minContentLength: 10,
    maxContentLength: 100000,
    requiredStructures: ['imports', 'exports']
  },
  
  RETRY_STRATEGIES: {
    exponentialBackoff: true,
    maxRetries: 3,
    baseDelay: 1000
  }
};
```

## 重构实施计划

### 第1周：目录结构重组
1. 创建新的目录结构
2. 移动现有文件到合适位置
3. 更新所有import路径

### 第2周：核心类拆分
1. 创建 `BuildFixOrchestrator` 替代 `BuildFixAgent`
2. 实现 `FixSession` 和 `FixAttempt`
3. 重构错误分析相关逻辑

### 第3周：服务层重构
1. 实现 `PromptBuilder` 和 `ConversationManager`
2. 重构 `ToolExecutor` 为更模块化的设计
3. 实现策略模式的修复策略

### 第4周：模板和配置外化
1. 提取所有硬编码的提示词模板
2. 外化配置参数
3. 实现配置热重载机制

### 第5周：测试和优化
1. 编写单元测试
2. 集成测试
3. 性能优化和代码审查

## 预期收益

### 1. 可维护性提升
- 代码行数减少50%以上（通过职责分离）
- 单个类复杂度降低70%
- 新增功能开发效率提升40%

### 2. 可测试性提升
- 单元测试覆盖率从30%提升到90%
- 集成测试更加稳定
- 调试和问题定位更加容易

### 3. 可扩展性提升
- 支持插件化的修复策略
- 支持自定义提示词模板
- 支持多种AI服务提供商

### 4. 用户体验提升
- 更清晰的错误信息和修复建议
- 更快的修复速度
- 更高的修复成功率

## 风险评估

### 1. 兼容性风险
- **风险**: 重构可能破坏现有API
- **缓解**: 保持向后兼容的适配器层

### 2. 性能风险
- **风险**: 过度抽象可能影响性能
- **缓解**: 性能基准测试和优化

### 3. 开发风险
- **风险**: 重构工作量大，可能影响其他功能开发
- **缓解**: 分阶段实施，保持主分支稳定

## 总结

这个重构计划将把当前的单体式 `BuildFixAgent` 重构为一个更加模块化、可维护和可扩展的架构。通过领域驱动设计、策略模式和依赖注入等设计模式，我们将显著提升代码质量和开发效率。

重构后的架构将更好地支持VueMigrator的长期发展，为未来添加更多修复策略和AI能力提供坚实的基础。
