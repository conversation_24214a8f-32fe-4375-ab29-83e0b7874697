{"buildCommand": "pnpm build:prod", "devCommand": "pnpm dev", "installCommand": "pnpm install", "maxAttempts": 10, "mode": "build", "devTimeout": 30000, "useLegacyPeerDeps": true, "skipInstall": false, "skipAI": false, "dryRun": false, "verbose": true, "showProgress": true, "errorPatterns": {"typescript": ["error TS\\d+:", "\\.ts\\(\\d+,\\d+\\):"], "vue": ["\\.vue:\\d+:\\d+:", "Vue warn"], "webpack": ["ERROR in", "<PERSON><PERSON><PERSON> not found"], "eslint": ["\\d+:\\d+\\s+(error|warning)"], "element-ui": ["element-ui", "element-plus"]}, "fixStrategies": {"missing-module": {"enabled": true, "priority": 1, "useAI": true}, "property-not-exist": {"enabled": true, "priority": 2, "useAI": true}, "vue-version": {"enabled": true, "priority": 3, "useAI": true}, "ui-library": {"enabled": true, "priority": 4, "useAI": true}}, "aiConfig": {"maxTokens": 8000, "temperature": 0.0, "maxRetries": 3, "timeout": 30000}, "moduleMapping": {"element-ui": "element-plus", "vue": "vue@next"}, "excludePatterns": ["node_modules/**", "dist/**", "build/**", "*.min.js", "*.map"], "backupConfig": {"enabled": true, "suffix": "build-fixer-backup", "maxBackups": 5}, "logging": {"level": "info", "logFile": "build-fixer.log", "enableFileLogging": true}}